<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NeuroScan Pro - Tableau de Bord</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-card.blue {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card.green {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .stat-card.purple {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        /* Fix pour les graphiques */
        .chart-container {
            position: relative;
            height: 320px;
            width: 100%;
        }

        .chart-container canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100% !important;
            height: 100% !important;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-brain text-3xl text-indigo-600 mr-3"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">NeuroScan Pro</h1>
                        <p class="text-sm text-gray-500">Tableau de bord professionnel</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <button id="exportBtn" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                            <i class="fas fa-download mr-2"></i>Exporter
                        </button>
                        <div id="exportMenu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                            <a href="/api/analytics/export/csv" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-t-lg">
                                <i class="fas fa-file-csv mr-2 text-green-600"></i>Exporter CSV
                            </a>
                            <a href="/api/analytics/export/json" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-b-lg">
                                <i class="fas fa-file-code mr-2 text-blue-600"></i>Exporter JSON
                            </a>
                        </div>
                    </div>
                    <a href="/pro-dashboard-advanced" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-rocket mr-2"></i>Mode Avancé
                    </a>
                    <a href="/" class="text-gray-600 hover:text-indigo-600 transition-colors">
                        <i class="fas fa-home mr-2"></i>Retour à l'analyse
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stat-card blue text-white p-6 rounded-2xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-blue-100 text-sm font-medium">Total Analyses</p>
                        <p id="totalAnalyses" class="text-3xl font-bold">-</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-full">
                        <i class="fas fa-chart-line text-2xl"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card green text-white p-6 rounded-2xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-green-100 text-sm font-medium">Confiance Moyenne</p>
                        <p id="avgConfidence" class="text-3xl font-bold">-%</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-full">
                        <i class="fas fa-check-circle text-2xl"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card purple text-white p-6 rounded-2xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-100 text-sm font-medium">Jours Actifs</p>
                        <p id="activeDays" class="text-3xl font-bold">-</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-full">
                        <i class="fas fa-calendar-alt text-2xl"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card text-white p-6 rounded-2xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-pink-100 text-sm font-medium">Temps Moyen</p>
                        <p id="avgProcessingTime" class="text-3xl font-bold">-s</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-full">
                        <i class="fas fa-clock text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Period Analysis Chart -->
            <div class="bg-white p-6 rounded-2xl shadow-lg card-hover">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-gray-800">Analyses par Période</h3>
                    <div class="flex space-x-2">
                        <button id="dayBtn" class="period-btn bg-indigo-600 text-white px-3 py-1 rounded-lg text-sm">Jour</button>
                        <button id="monthBtn" class="period-btn bg-gray-200 text-gray-700 px-3 py-1 rounded-lg text-sm">Mois</button>
                        <button id="yearBtn" class="period-btn bg-gray-200 text-gray-700 px-3 py-1 rounded-lg text-sm">Année</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="periodChart"></canvas>
                </div>
            </div>

            <!-- Tumor Distribution Chart -->
            <div class="bg-white p-6 rounded-2xl shadow-lg card-hover">
                <h3 class="text-xl font-bold text-gray-800 mb-6">Répartition des Diagnostics</h3>
                <div class="chart-container">
                    <canvas id="tumorChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Analyses Table -->
        <div class="bg-white rounded-2xl shadow-lg card-hover">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-xl font-bold text-gray-800">Analyses Récentes</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date/Heure</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fichier</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Diagnostic</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Confiance</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Temps</th>
                        </tr>
                    </thead>
                    <tbody id="recentAnalysesTable" class="bg-white divide-y divide-gray-200">
                        <!-- Data will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <script>
        let periodChart = null;
        let tumorChart = null;
        let currentPeriod = 'day';

        // Load dashboard data
        async function loadDashboardData() {
            try {
                // Load overview data
                const overviewResponse = await fetch('/api/analytics/overview');
                const overviewData = await overviewResponse.json();
                
                if (overviewData.success) {
                    updateOverviewStats(overviewData.data);
                    updateTumorChart(overviewData.data.tumor_distribution);
                }

                // Load period data
                await loadPeriodData(currentPeriod);

                // Load recent analyses
                const recentResponse = await fetch('/api/analytics/recent');
                const recentData = await recentResponse.json();
                
                if (recentData.success) {
                    updateRecentAnalysesTable(recentData.data);
                }

            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        function updateOverviewStats(data) {
            document.getElementById('totalAnalyses').textContent = data.total_analyses;
            document.getElementById('avgConfidence').textContent = data.avg_confidence + '%';
            document.getElementById('activeDays').textContent = data.active_days;
            document.getElementById('avgProcessingTime').textContent = data.avg_processing_time + 's';
        }

        async function loadPeriodData(period) {
            try {
                const response = await fetch(`/api/analytics/period/${period}`);
                const data = await response.json();

                if (data.success) {
                    updatePeriodChart(data.data);
                } else {
                    console.error('Error from API:', data.error);
                    // Afficher un graphique vide avec message d'erreur
                    updatePeriodChart({
                        labels: ['Aucune donnée'],
                        values: [0],
                        period: period
                    });
                }
            } catch (error) {
                console.error('Error loading period data:', error);
                // Afficher un graphique vide en cas d'erreur réseau
                updatePeriodChart({
                    labels: ['Erreur de chargement'],
                    values: [0],
                    period: period
                });
            }
        }

        function updatePeriodChart(data) {
            const ctx = document.getElementById('periodChart').getContext('2d');

            if (periodChart) {
                periodChart.destroy();
            }

            // Déterminer le titre basé sur la période
            let chartTitle = 'Analyses';
            if (data.period === 'day') {
                chartTitle = 'Analyses par heure';
            } else if (data.period === 'month') {
                chartTitle = 'Analyses par jour';
            } else if (data.period === 'year') {
                chartTitle = 'Analyses par mois';
            }

            periodChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: chartTitle,
                        data: data.values,
                        borderColor: 'rgb(99, 102, 241)',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: 'rgb(99, 102, 241)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: data.period === 'day' ? 'Heures' :
                                      data.period === 'month' ? 'Jours' : 'Mois'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Nombre d\'analyses'
                            },
                            ticks: {
                                stepSize: 1,
                                precision: 0
                            }
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    },
                    layout: {
                        padding: {
                            top: 10,
                            bottom: 10
                        }
                    }
                }
            });
        }

        function updateTumorChart(distribution) {
            const ctx = document.getElementById('tumorChart').getContext('2d');
            
            if (tumorChart) {
                tumorChart.destroy();
            }

            const labels = Object.keys(distribution);
            const values = Object.values(distribution);
            const colors = ['#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

            tumorChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: colors,
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    layout: {
                        padding: {
                            top: 10,
                            bottom: 10
                        }
                    }
                }
            });
        }

        function updateRecentAnalysesTable(analyses) {
            const tbody = document.getElementById('recentAnalysesTable');
            tbody.innerHTML = '';

            analyses.forEach(analysis => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${new Date(analysis.timestamp).toLocaleString('fr-FR')}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${analysis.filename}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getBadgeClass(analysis.predicted_label)}">
                            ${analysis.predicted_label}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${analysis.confidence}%
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${analysis.processing_time}s
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function getBadgeClass(label) {
            const classes = {
                'Normal': 'bg-green-100 text-green-800',
                'Gliome': 'bg-red-100 text-red-800',
                'Méningiome': 'bg-yellow-100 text-yellow-800',
                'Tumeur pituitaire': 'bg-purple-100 text-purple-800'
            };
            return classes[label] || 'bg-gray-100 text-gray-800';
        }

        // Export menu handler
        document.addEventListener('DOMContentLoaded', function() {
            const exportBtn = document.getElementById('exportBtn');
            const exportMenu = document.getElementById('exportMenu');

            exportBtn?.addEventListener('click', function(e) {
                e.stopPropagation();
                exportMenu.classList.toggle('hidden');
            });

            // Close menu when clicking outside
            document.addEventListener('click', function() {
                exportMenu.classList.add('hidden');
            });

            // Period button handlers
            const periodButtons = document.querySelectorAll('.period-btn');
            
            periodButtons.forEach(btn => {
                btn.addEventListener('click', async function() {
                    // Update button styles
                    periodButtons.forEach(b => {
                        b.classList.remove('bg-indigo-600', 'text-white');
                        b.classList.add('bg-gray-200', 'text-gray-700');
                    });
                    this.classList.remove('bg-gray-200', 'text-gray-700');
                    this.classList.add('bg-indigo-600', 'text-white');
                    
                    // Update period and reload data
                    currentPeriod = this.id.replace('Btn', '');
                    await loadPeriodData(currentPeriod);
                });
            });

            // Load initial data
            loadDashboardData();
            
            // Auto-refresh every 30 seconds
            setInterval(loadDashboardData, 30000);
        });
    </script>
</body>
</html>
