<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mes Patients | NeuroScan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        .patient-card {
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }
        .patient-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .risk-badge {
            font-size: 0.8rem;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
        }
        .risk-faible { background-color: #d4edda; color: #155724; }
        .risk-modéré { background-color: #fff3cd; color: #856404; }
        .risk-élevé { background-color: #f8d7da; color: #721c24; }
        .risk-critique { background-color: #721c24; color: white; }
        .stats-card {
            border-left: 4px solid #007bff;
        }
        .search-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-brain me-2"></i>NeuroScan
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">Dr. {{ doctor.full_name }}</span>
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-arrow-left me-1"></i>Tableau de bord
                </a>
            </div>
        </div>
    </nav>

    <!-- Section de recherche -->
    <div class="search-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-users me-3"></i>Mes Patients
                    </h1>
                    <p class="mb-0 fs-5">Suivi et historique détaillé de vos patients</p>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchInput" placeholder="Rechercher un patient...">
                        <button class="btn btn-light" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Statistiques rapides -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x text-primary mb-2"></i>
                        <h5 class="card-title">Total Patients</h5>
                        <h3 class="text-primary" id="totalPatients">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                        <h5 class="card-title">Patients à Risque</h5>
                        <h3 class="text-warning" id="highRiskPatients">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-check fa-2x text-success mb-2"></i>
                        <h5 class="card-title">Analyses ce Mois</h5>
                        <h3 class="text-success" id="monthlyAnalyses">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                        <h5 class="card-title">Suivi Moyen</h5>
                        <h3 class="text-info" id="avgFollowUp">-</h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtres -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <label class="form-label">Filtrer par diagnostic:</label>
                                <select class="form-select" id="diagnosisFilter">
                                    <option value="">Tous les diagnostics</option>
                                    <option value="Normal">Normal</option>
                                    <option value="Gliome">Gliome</option>
                                    <option value="Méningiome">Méningiome</option>
                                    <option value="Tumeur pituitaire">Tumeur pituitaire</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Filtrer par niveau de risque:</label>
                                <select class="form-select" id="riskFilter">
                                    <option value="">Tous les niveaux</option>
                                    <option value="critique">Critique</option>
                                    <option value="élevé">Élevé</option>
                                    <option value="modéré">Modéré</option>
                                    <option value="faible">Faible</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Trier par:</label>
                                <select class="form-select" id="sortBy">
                                    <option value="last_analysis_date">Dernière analyse</option>
                                    <option value="patient_name">Nom du patient</option>
                                    <option value="total_analyses">Nombre d'analyses</option>
                                    <option value="risk_level">Niveau de risque</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn btn-primary" onclick="applyFilters()">
                                        <i class="fas fa-filter me-1"></i>Appliquer
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Liste des patients -->
        <div class="row" id="patientsContainer">
            <!-- Patients chargés dynamiquement -->
        </div>

        <!-- Pagination -->
        <div class="row mt-4">
            <div class="col-12">
                <nav aria-label="Navigation des patients">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination dynamique -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Modal pour actions rapides -->
    <div class="modal fade" id="quickActionsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Actions Rapides</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="quickActionsBody">
                    <!-- Contenu dynamique -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script>
        let allPatients = [];
        let filteredPatients = [];
        let currentPage = 1;
        const patientsPerPage = 12;

        document.addEventListener('DOMContentLoaded', function() {
            loadPatients();
            setupEventListeners();
        });

        function setupEventListeners() {
            document.getElementById('searchInput').addEventListener('input', function() {
                filterPatients();
            });
        }

        async function loadPatients() {
            try {
                const response = await fetch('/api/my-patients');
                const data = await response.json();

                if (data.success) {
                    allPatients = data.data;
                    
                    // Charger les métriques pour chaque patient
                    for (let patient of allPatients) {
                        await loadPatientMetrics(patient);
                    }
                    
                    filteredPatients = [...allPatients];
                    displayPatients();
                    updateStats();
                } else {
                    console.error('Erreur lors du chargement des patients:', data.error);
                }
            } catch (error) {
                console.error('Erreur lors du chargement des patients:', error);
            }
        }

        async function loadPatientMetrics(patient) {
            try {
                const response = await fetch(`/api/patients/${patient.patient_id}/detailed-history`);
                const data = await response.json();

                if (data.success) {
                    patient.metrics = data.data.metrics;
                    patient.last_diagnosis = data.data.analyses.length > 0 ? 
                        data.data.analyses[data.data.analyses.length - 1].predicted_label : 'Inconnu';
                }
            } catch (error) {
                console.error(`Erreur lors du chargement des métriques pour ${patient.patient_id}:`, error);
                patient.metrics = { risk_level: 'indéterminé' };
                patient.last_diagnosis = 'Inconnu';
            }
        }

        function displayPatients() {
            const container = document.getElementById('patientsContainer');
            const startIndex = (currentPage - 1) * patientsPerPage;
            const endIndex = startIndex + patientsPerPage;
            const patientsToShow = filteredPatients.slice(startIndex, endIndex);

            container.innerHTML = patientsToShow.map(patient => `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card patient-card h-100" onclick="viewPatientProfile('${patient.patient_id}')">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h5 class="card-title mb-0">${patient.patient_name}</h5>
                                <span class="risk-badge risk-${patient.metrics?.risk_level || 'indéterminé'}">
                                    ${(patient.metrics?.risk_level || 'indéterminé').charAt(0).toUpperCase() + (patient.metrics?.risk_level || 'indéterminé').slice(1)}
                                </span>
                            </div>
                            <p class="card-text text-muted mb-2">
                                <small>ID: ${patient.patient_id}</small>
                            </p>
                            <div class="row text-center mb-3">
                                <div class="col-4">
                                    <div class="border-end">
                                        <strong class="d-block">${patient.total_analyses}</strong>
                                        <small class="text-muted">Analyses</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="border-end">
                                        <strong class="d-block">${patient.metrics?.follow_up_months || 0}</strong>
                                        <small class="text-muted">Mois</small>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <strong class="d-block">${patient.metrics?.avg_confidence || 0}%</strong>
                                    <small class="text-muted">Confiance</small>
                                </div>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">Dernier diagnostic:</small>
                                <span class="badge bg-${patient.last_diagnosis === 'Normal' ? 'success' : 'warning'} ms-1">
                                    ${patient.last_diagnosis}
                                </span>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">Dernière analyse: ${patient.last_analysis_date}</small>
                            </div>
                            ${patient.metrics?.alerts && patient.metrics.alerts.length > 0 ? `
                                <div class="alert alert-warning alert-sm py-1 px-2 mb-2">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    <small>${patient.metrics.alerts.length} alerte(s)</small>
                                </div>
                            ` : ''}
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); viewPatientProfile('${patient.patient_id}')">
                                    <i class="fas fa-eye me-1"></i>Profil
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" onclick="event.stopPropagation(); showQuickActions('${patient.patient_id}')">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            updatePagination();
        }

        function updateStats() {
            document.getElementById('totalPatients').textContent = allPatients.length;
            
            const highRiskCount = allPatients.filter(p => 
                p.metrics?.risk_level === 'critique' || p.metrics?.risk_level === 'élevé'
            ).length;
            document.getElementById('highRiskPatients').textContent = highRiskCount;
            
            // Calcul des analyses du mois (simulation)
            const monthlyCount = allPatients.reduce((sum, p) => sum + (p.total_analyses || 0), 0);
            document.getElementById('monthlyAnalyses').textContent = Math.floor(monthlyCount / 3);
            
            // Suivi moyen
            const avgFollowUp = allPatients.reduce((sum, p) => sum + (p.metrics?.follow_up_months || 0), 0) / allPatients.length;
            document.getElementById('avgFollowUp').textContent = avgFollowUp.toFixed(1) + ' mois';
        }

        function filterPatients() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const diagnosisFilter = document.getElementById('diagnosisFilter').value;
            const riskFilter = document.getElementById('riskFilter').value;

            filteredPatients = allPatients.filter(patient => {
                const matchesSearch = patient.patient_name.toLowerCase().includes(searchTerm) ||
                                    patient.patient_id.toLowerCase().includes(searchTerm);
                const matchesDiagnosis = !diagnosisFilter || patient.last_diagnosis === diagnosisFilter;
                const matchesRisk = !riskFilter || patient.metrics?.risk_level === riskFilter;

                return matchesSearch && matchesDiagnosis && matchesRisk;
            });

            currentPage = 1;
            displayPatients();
        }

        function applyFilters() {
            filterPatients();
        }

        function updatePagination() {
            const totalPages = Math.ceil(filteredPatients.length / patientsPerPage);
            const pagination = document.getElementById('pagination');

            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHTML = '';
            
            // Bouton précédent
            paginationHTML += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Précédent</a>
                </li>
            `;

            // Pages
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHTML += `
                        <li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                        </li>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
                }
            }

            // Bouton suivant
            paginationHTML += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Suivant</a>
                </li>
            `;

            pagination.innerHTML = paginationHTML;
        }

        function changePage(page) {
            const totalPages = Math.ceil(filteredPatients.length / patientsPerPage);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                displayPatients();
            }
        }

        function viewPatientProfile(patientId) {
            window.location.href = `/patient/${patientId}`;
        }

        function showQuickActions(patientId) {
            const patient = allPatients.find(p => p.patient_id === patientId);
            if (!patient) return;

            const modalBody = document.getElementById('quickActionsBody');
            modalBody.innerHTML = `
                <h6>${patient.patient_name}</h6>
                <p class="text-muted">ID: ${patient.patient_id}</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="viewPatientProfile('${patientId}')">
                        <i class="fas fa-eye me-2"></i>Voir le profil complet
                    </button>
                    <button class="btn btn-success" onclick="scheduleFollowUp('${patientId}')">
                        <i class="fas fa-calendar-plus me-2"></i>Programmer un suivi
                    </button>
                    <button class="btn btn-info" onclick="generateReport('${patientId}')">
                        <i class="fas fa-file-medical me-2"></i>Générer un rapport
                    </button>
                </div>
            `;

            new bootstrap.Modal(document.getElementById('quickActionsModal')).show();
        }

        function scheduleFollowUp(patientId) {
            // Fonctionnalité à implémenter
            alert('Fonctionnalité de programmation de suivi à venir');
        }

        function generateReport(patientId) {
            // Fonctionnalité à implémenter
            alert('Fonctionnalité de génération de rapport à venir');
        }
    </script>
</body>
</html>
