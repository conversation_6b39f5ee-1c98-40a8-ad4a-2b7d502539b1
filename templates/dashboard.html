<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - NeuroScan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo et titre -->
                <div class="flex items-center">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-brain text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900">NeuroScan</h1>
                            <p class="text-xs text-gray-500">Dashboard Médecin</p>
                        </div>
                    </div>
                </div>

                <!-- Informations utilisateur -->
                <div class="flex items-center space-x-4">
                    <!-- Notifications et alertes -->
                    <div class="relative">
                        <button id="alertsButton" class="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none">
                            <i class="fas fa-bell text-lg"></i>
                            <span id="alertsBadge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">0</span>
                        </button>
                        <!-- Dropdown des alertes -->
                        <div id="alertsDropdown" class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border hidden z-50">
                            <div class="p-4 border-b">
                                <h3 class="text-lg font-semibold text-gray-900">Alertes Médicales</h3>
                            </div>
                            <div id="alertsList" class="max-h-96 overflow-y-auto">
                                <!-- Alertes chargées dynamiquement -->
                            </div>
                            <div class="p-3 border-t text-center">
                                <a href="/alerts" class="text-blue-600 hover:text-blue-800 text-sm font-medium">Voir toutes les alertes</a>
                            </div>
                        </div>
                    </div>

                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900">Dr. {{ doctor.full_name }}</p>
                        <p class="text-xs text-gray-500">{{ doctor.specialty or 'Médecin' }}</p>
                    </div>
                    <div class="relative">
                        <button class="flex items-center space-x-2 text-gray-700 hover:text-gray-900 focus:outline-none">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-user-md text-blue-600 text-sm"></i>
                            </div>
                        </button>
                    </div>
                    <a href="{{ url_for('logout') }}" 
                       class="text-gray-500 hover:text-red-600 transition-colors duration-200"
                       title="Déconnexion">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Messages flash -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
                {% for category, message in messages %}
                    <div class="mb-4 p-4 rounded-lg {% if category == 'error' %}bg-red-50 text-red-700 border border-red-200{% elif category == 'success' %}bg-green-50 text-green-700 border border-green-200{% else %}bg-blue-50 text-blue-700 border border-blue-200{% endif %}">
                        <div class="flex items-center">
                            <i class="fas {% if category == 'error' %}fa-exclamation-circle{% elif category == 'success' %}fa-check-circle{% else %}fa-info-circle{% endif %} mr-2"></i>
                            {{ message }}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Contenu principal -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Bienvenue -->
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">
                Bienvenue, Dr. {{ doctor.first_name }}
            </h2>
            <p class="text-gray-600">
                Accédez à vos outils d'analyse d'imagerie cérébrale et gérez vos patients.
            </p>
        </div>

        <!-- Actions rapides -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Nouvelle analyse -->
            <div class="card-hover bg-white rounded-xl shadow-sm border p-6 cursor-pointer" onclick="window.location.href='{{ url_for('index') }}'">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-upload text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Nouvelle Analyse</h3>
                        <p class="text-sm text-gray-500">Analyser une IRM cérébrale</p>
                    </div>
                </div>
                <p class="text-gray-600 text-sm">
                    Téléchargez et analysez une nouvelle image IRM pour détecter d'éventuelles tumeurs cérébrales.
                </p>
            </div>

            <!-- Tableau de bord pro -->
            <div class="card-hover bg-white rounded-xl shadow-sm border p-6 cursor-pointer" onclick="window.location.href='{{ url_for('pro_dashboard') }}'">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-chart-bar text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Statistiques</h3>
                        <p class="text-sm text-gray-500">Analyses et tendances</p>
                    </div>
                </div>
                <p class="text-gray-600 text-sm">
                    Consultez les statistiques détaillées de vos analyses et suivez les tendances.
                </p>
            </div>

            <!-- Suivi patients -->
            <div class="card-hover bg-white rounded-xl shadow-sm border p-6 cursor-pointer" onclick="window.location.href='{{ url_for('patients_list') }}'">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-users text-purple-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Mes Patients</h3>
                        <p class="text-sm text-gray-500">Suivi détaillé</p>
                    </div>
                </div>
                <p class="text-gray-600 text-sm">
                    Accédez à la liste complète de vos patients avec historique et évolution détaillée.
                </p>
            </div>

            <!-- Statistiques plateforme -->
            <div class="card-hover bg-white rounded-xl shadow-sm border p-6 cursor-pointer" onclick="window.location.href='{{ url_for('platform_stats') }}'">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-globe text-indigo-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Stats Plateforme</h3>
                        <p class="text-sm text-gray-500">Vue d'ensemble globale</p>
                    </div>
                </div>
                <p class="text-gray-600 text-sm">
                    Consultez les statistiques générales de tous les médecins utilisant NeuroScan.
                </p>
            </div>
        </div>

        <!-- Informations du compte -->
        <div class="bg-white rounded-xl shadow-sm border p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-user-circle mr-2"></i>Informations du compte
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <div class="space-y-3">
                        <div>
                            <label class="text-sm font-medium text-gray-500">Nom complet</label>
                            <p class="text-gray-900">Dr. {{ doctor.full_name }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Email</label>
                            <p class="text-gray-900">{{ doctor.email }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Spécialité</label>
                            <p class="text-gray-900">{{ doctor.specialty or 'Non spécifiée' }}</p>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="space-y-3">
                        <div>
                            <label class="text-sm font-medium text-gray-500">Établissement</label>
                            <p class="text-gray-900">{{ doctor.hospital or 'Non spécifié' }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Numéro RPPS/ADELI</label>
                            <p class="text-gray-900">{{ doctor.license_number or 'Non spécifié' }}</p>
                        </div>
                        <div>
                            <label class="text-sm font-medium text-gray-500">Statut</label>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check-circle mr-1"></i>Actif
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Aide et support -->
        <div class="mt-8 bg-blue-50 rounded-xl border border-blue-200 p-6">
            <div class="flex items-start">
                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4 mt-1">
                    <i class="fas fa-question-circle text-blue-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-blue-900 mb-2">Besoin d'aide ?</h3>
                    <p class="text-blue-700 mb-4">
                        Notre équipe est là pour vous accompagner dans l'utilisation de NeuroScan.
                    </p>
                    <div class="flex flex-wrap gap-3">
                        <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                            <i class="fas fa-book mr-2"></i>Guide d'utilisation
                        </button>
                        <button class="bg-white hover:bg-blue-50 text-blue-600 border border-blue-300 px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                            <i class="fas fa-envelope mr-2"></i>Contacter le support
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let alertsData = [];

        // Animation d'entrée et initialisation
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card-hover');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Charger les alertes
            loadAlerts();

            // Actualiser les alertes toutes les 30 secondes
            setInterval(loadAlerts, 30000);

            // Gérer le dropdown des alertes
            setupAlertsDropdown();
        });

        async function loadAlerts() {
            try {
                const response = await fetch('/api/alerts');
                const data = await response.json();

                if (data.success) {
                    alertsData = data.data;
                    updateAlertsUI();
                }
            } catch (error) {
                console.error('Erreur lors du chargement des alertes:', error);
            }
        }

        function updateAlertsUI() {
            const badge = document.getElementById('alertsBadge');
            const alertsList = document.getElementById('alertsList');

            // Mettre à jour le badge
            const unreadCount = alertsData.filter(alert => !alert.is_read).length;
            if (unreadCount > 0) {
                badge.textContent = unreadCount;
                badge.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
            }

            // Mettre à jour la liste des alertes
            if (alertsData.length === 0) {
                alertsList.innerHTML = `
                    <div class="p-4 text-center text-gray-500">
                        <i class="fas fa-check-circle text-green-500 text-2xl mb-2"></i>
                        <p>Aucune alerte active</p>
                    </div>
                `;
            } else {
                alertsList.innerHTML = alertsData.slice(0, 5).map(alert => `
                    <div class="p-3 border-b hover:bg-gray-50 cursor-pointer ${!alert.is_read ? 'bg-blue-50' : ''}"
                         onclick="handleAlertClick(${alert.id})">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-${getSeverityIcon(alert.severity)} text-${getSeverityColor(alert.severity)} text-lg"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">${alert.title}</p>
                                <p class="text-xs text-gray-600 mt-1">${alert.patient_name} - ${alert.patient_id}</p>
                                <p class="text-xs text-gray-500 mt-1">${formatDate(alert.created_at)}</p>
                                ${!alert.is_read ? '<div class="w-2 h-2 bg-blue-500 rounded-full mt-1"></div>' : ''}
                            </div>
                        </div>
                    </div>
                `).join('');
            }
        }

        function setupAlertsDropdown() {
            const button = document.getElementById('alertsButton');
            const dropdown = document.getElementById('alertsDropdown');

            button.addEventListener('click', function(e) {
                e.stopPropagation();
                dropdown.classList.toggle('hidden');
            });

            // Fermer le dropdown en cliquant ailleurs
            document.addEventListener('click', function() {
                dropdown.classList.add('hidden');
            });

            dropdown.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }

        async function handleAlertClick(alertId) {
            try {
                // Marquer comme lue
                await fetch(`/api/alerts/${alertId}/mark-read`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                // Trouver l'alerte et rediriger vers le patient
                const alert = alertsData.find(a => a.id === alertId);
                if (alert) {
                    window.location.href = `/patient/${alert.patient_id}`;
                }
            } catch (error) {
                console.error('Erreur lors du traitement de l\'alerte:', error);
            }
        }

        function getSeverityIcon(severity) {
            switch (severity) {
                case 'high': return 'exclamation-triangle';
                case 'medium': return 'exclamation-circle';
                case 'low': return 'info-circle';
                default: return 'bell';
            }
        }

        function getSeverityColor(severity) {
            switch (severity) {
                case 'high': return 'red-500';
                case 'medium': return 'yellow-500';
                case 'low': return 'blue-500';
                default: return 'gray-500';
            }
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMins / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffMins < 1) return 'À l\'instant';
            if (diffMins < 60) return `Il y a ${diffMins} min`;
            if (diffHours < 24) return `Il y a ${diffHours}h`;
            if (diffDays < 7) return `Il y a ${diffDays}j`;
            return date.toLocaleDateString('fr-FR');
        }
    </script>
</body>
</html>
