<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profil Patient - {{ patient.patient_name }} | NeuroScan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        .patient-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .metric-card {
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        .metric-card:hover {
            transform: translateY(-2px);
        }
        .alert-card {
            border-left: 4px solid #dc3545;
        }
        .evolution-timeline {
            position: relative;
            padding-left: 2rem;
        }
        .evolution-timeline::before {
            content: '';
            position: absolute;
            left: 1rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #dee2e6;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 2rem;
            padding-left: 2rem;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -0.5rem;
            top: 0.5rem;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background: #007bff;
            border: 3px solid white;
            box-shadow: 0 0 0 3px #dee2e6;
        }
        .risk-badge {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
        }
        .risk-faible { background-color: #d4edda; color: #155724; }
        .risk-modéré { background-color: #fff3cd; color: #856404; }
        .risk-élevé { background-color: #f8d7da; color: #721c24; }
        .risk-critique { background-color: #721c24; color: white; }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-brain me-2"></i>NeuroScan
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">Dr. {{ doctor.full_name }}</span>
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-arrow-left me-1"></i>Retour au tableau de bord
                </a>
            </div>
        </div>
    </nav>

    <!-- En-tête patient -->
    <div class="patient-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-user-circle me-3"></i>{{ patient.patient_name }}
                    </h1>
                    <p class="mb-0 fs-5">ID Patient: {{ patient.patient_id }}</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex flex-column">
                        <span class="mb-1">Première analyse: {{ patient.first_analysis_date }}</span>
                        <span class="mb-1">Dernière analyse: {{ patient.last_analysis_date }}</span>
                        <span>Total analyses: <strong>{{ patient.total_analyses }}</strong></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Alertes et niveau de risque -->
        <div id="alertsSection" class="row mb-4" style="display: none;">
            <div class="col-12">
                <div class="card alert-card">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Alertes Médicales</h5>
                    </div>
                    <div class="card-body" id="alertsContent">
                        <!-- Alertes dynamiques -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Métriques principales -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-alt fa-2x text-primary mb-2"></i>
                        <h5 class="card-title">Suivi</h5>
                        <p class="card-text" id="followUpPeriod">-</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x text-success mb-2"></i>
                        <h5 class="card-title">Confiance Moyenne</h5>
                        <p class="card-text" id="avgConfidence">-</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-stethoscope fa-2x text-info mb-2"></i>
                        <h5 class="card-title">Diagnostic Principal</h5>
                        <p class="card-text" id="mainDiagnosis">-</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-shield-alt fa-2x mb-2"></i>
                        <h5 class="card-title">Niveau de Risque</h5>
                        <p class="card-text">
                            <span id="riskLevel" class="risk-badge">-</span>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graphiques d'évolution -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Évolution de la Confiance</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="confidenceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-expand-arrows-alt me-2"></i>Évolution de la Taille</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="sizeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Historique détaillé -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Historique Détaillé des Analyses</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="analysesTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Date</th>
                                        <th>Diagnostic</th>
                                        <th>Confiance</th>
                                        <th>Taille Estimée</th>
                                        <th>Évolution</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="analysesTableBody">
                                    <!-- Données dynamiques -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Timeline d'évolution -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-timeline me-2"></i>Timeline d'Évolution</h5>
                    </div>
                    <div class="card-body">
                        <div id="evolutionTimeline" class="evolution-timeline">
                            <!-- Timeline dynamique -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommandations -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Recommandations</h5>
                    </div>
                    <div class="card-body" id="recommendationsContent">
                        <!-- Recommandations dynamiques -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour détails d'analyse -->
    <div class="modal fade" id="analysisModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Détails de l'Analyse</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="analysisModalBody">
                    <!-- Contenu dynamique -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const patientId = '{{ patient.patient_id }}';
        let patientData = null;
        let confidenceChart = null;
        let sizeChart = null;

        // Charger les données du patient au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            loadPatientData();
        });

        async function loadPatientData() {
            try {
                const response = await fetch(`/api/patients/${patientId}/detailed-history`);
                const data = await response.json();

                if (data.success) {
                    patientData = data.data;
                    displayPatientMetrics(patientData.metrics);
                    displayAlerts(patientData.metrics.alerts);
                    displayRecommendations(patientData.metrics.recommendations);
                    createCharts(patientData.analyses);
                    displayAnalysesTable(patientData.analyses, patientData.evolution_details);
                    displayEvolutionTimeline(patientData.analyses, patientData.evolution_details);
                } else {
                    console.error('Erreur lors du chargement des données:', data.error);
                }
            } catch (error) {
                console.error('Erreur lors du chargement des données:', error);
            }
        }

        function displayPatientMetrics(metrics) {
            document.getElementById('followUpPeriod').textContent = 
                `${metrics.follow_up_months} mois (${metrics.follow_up_days} jours)`;
            document.getElementById('avgConfidence').textContent = `${metrics.avg_confidence}%`;
            document.getElementById('mainDiagnosis').textContent = metrics.most_common_diagnosis;
            
            const riskElement = document.getElementById('riskLevel');
            riskElement.textContent = metrics.risk_level.charAt(0).toUpperCase() + metrics.risk_level.slice(1);
            riskElement.className = `risk-badge risk-${metrics.risk_level}`;
        }

        function displayAlerts(alerts) {
            const alertsSection = document.getElementById('alertsSection');
            const alertsContent = document.getElementById('alertsContent');

            if (alerts && alerts.length > 0) {
                alertsSection.style.display = 'block';
                alertsContent.innerHTML = alerts.map(alert => `
                    <div class="alert alert-${alert.severity === 'high' ? 'danger' : alert.severity === 'medium' ? 'warning' : 'info'} mb-2">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>${alert.type.replace('_', ' ').toUpperCase()}:</strong> ${alert.message}
                    </div>
                `).join('');
            } else {
                alertsSection.style.display = 'none';
            }
        }

        function displayRecommendations(recommendations) {
            const content = document.getElementById('recommendationsContent');

            if (recommendations && recommendations.length > 0) {
                content.innerHTML = `
                    <ul class="list-unstyled">
                        ${recommendations.map(rec => `
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                ${rec}
                            </li>
                        `).join('')}
                    </ul>
                `;
            } else {
                content.innerHTML = '<p class="text-muted">Aucune recommandation spécifique pour le moment.</p>';
            }
        }

        function createCharts(analyses) {
            if (!analyses || analyses.length === 0) return;

            const dates = analyses.map(a => a.exam_date);
            const confidences = analyses.map(a => a.confidence);
            const sizes = analyses.map(a => a.tumor_size_estimate || 0);

            // Graphique de confiance
            const confidenceCtx = document.getElementById('confidenceChart').getContext('2d');
            if (confidenceChart) confidenceChart.destroy();

            confidenceChart = new Chart(confidenceCtx, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [{
                        label: 'Confiance (%)',
                        data: confidences,
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: {
                                display: true,
                                text: 'Confiance (%)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Date d\'examen'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // Graphique de taille
            const sizeCtx = document.getElementById('sizeChart').getContext('2d');
            if (sizeChart) sizeChart.destroy();

            const sizesFiltered = analyses.filter(a => a.tumor_size_estimate).map(a => ({
                x: a.exam_date,
                y: a.tumor_size_estimate
            }));

            sizeChart = new Chart(sizeCtx, {
                type: 'line',
                data: {
                    datasets: [{
                        label: 'Taille estimée (cm)',
                        data: sizesFiltered,
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Taille (cm)'
                            }
                        },
                        x: {
                            type: 'time',
                            time: {
                                parser: 'YYYY-MM-DD',
                                displayFormats: {
                                    day: 'DD/MM/YYYY'
                                }
                            },
                            title: {
                                display: true,
                                text: 'Date d\'examen'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function displayAnalysesTable(analyses, evolutionDetails) {
            const tbody = document.getElementById('analysesTableBody');

            tbody.innerHTML = analyses.map((analysis, index) => {
                const evolution = evolutionDetails.find(e => e.exam_date === analysis.exam_date);
                const prevAnalysis = index > 0 ? analyses[index - 1] : null;

                let evolutionBadge = '';
                if (evolution) {
                    const badgeClass = {
                        'amélioration': 'success',
                        'dégradation': 'danger',
                        'croissance': 'warning',
                        'réduction': 'info',
                        'stable': 'secondary'
                    }[evolution.evolution_type] || 'secondary';

                    evolutionBadge = `<span class="badge bg-${badgeClass}">${evolution.evolution_type}</span>`;
                }

                return `
                    <tr>
                        <td>${analysis.exam_date}</td>
                        <td>
                            <span class="badge bg-${analysis.predicted_label === 'Normal' ? 'success' : 'warning'}">
                                ${analysis.predicted_label}
                            </span>
                        </td>
                        <td>${analysis.confidence}%</td>
                        <td>${analysis.tumor_size_estimate ? analysis.tumor_size_estimate + ' cm' : '-'}</td>
                        <td>${evolutionBadge}</td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="showAnalysisDetails(${analysis.id})">
                                <i class="fas fa-eye"></i> Détails
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        function displayEvolutionTimeline(analyses, evolutionDetails) {
            const timeline = document.getElementById('evolutionTimeline');

            timeline.innerHTML = analyses.map((analysis, index) => {
                const evolution = evolutionDetails.find(e => e.exam_date === analysis.exam_date);

                return `
                    <div class="timeline-item">
                        <div class="card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0">${analysis.exam_date}</h6>
                                    <span class="badge bg-${analysis.predicted_label === 'Normal' ? 'success' : 'warning'}">
                                        ${analysis.predicted_label}
                                    </span>
                                </div>
                                <p class="card-text">
                                    <strong>Confiance:</strong> ${analysis.confidence}%<br>
                                    ${analysis.tumor_size_estimate ? `<strong>Taille:</strong> ${analysis.tumor_size_estimate} cm<br>` : ''}
                                    ${evolution ? `<strong>Évolution:</strong> ${evolution.notes}` : ''}
                                </p>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function showAnalysisDetails(analysisId) {
            const analysis = patientData.analyses.find(a => a.id === analysisId);
            if (!analysis) return;

            const modalBody = document.getElementById('analysisModalBody');
            modalBody.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Informations Générales</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Date d'examen:</strong></td><td>${analysis.exam_date}</td></tr>
                            <tr><td><strong>Fichier:</strong></td><td>${analysis.filename}</td></tr>
                            <tr><td><strong>Temps de traitement:</strong></td><td>${analysis.processing_time}s</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Diagnostic</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Diagnostic:</strong></td><td>${analysis.predicted_label}</td></tr>
                            <tr><td><strong>Confiance:</strong></td><td>${analysis.confidence}%</td></tr>
                            <tr><td><strong>Taille estimée:</strong></td><td>${analysis.tumor_size_estimate ? analysis.tumor_size_estimate + ' cm' : 'N/A'}</td></tr>
                        </table>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Probabilités par classe</h6>
                        <div class="row">
                            ${Object.entries(analysis.probabilities).map(([label, prob]) => `
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <div class="progress mb-1" style="height: 20px;">
                                            <div class="progress-bar" style="width: ${(prob * 100).toFixed(1)}%">
                                                ${(prob * 100).toFixed(1)}%
                                            </div>
                                        </div>
                                        <small>${label}</small>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                ${analysis.description ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Description</h6>
                            <p>${analysis.description}</p>
                        </div>
                    </div>
                ` : ''}
                ${analysis.recommendations && analysis.recommendations.length > 0 ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Recommandations</h6>
                            <ul>
                                ${analysis.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                ` : ''}
            `;

            new bootstrap.Modal(document.getElementById('analysisModal')).show();
        }
    </script>
</body>
</html>
