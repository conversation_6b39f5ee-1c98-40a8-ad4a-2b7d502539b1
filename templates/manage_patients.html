<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Patients | NeuroScan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .patient-card {
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }
        .patient-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .action-buttons .btn {
            margin: 0 2px;
        }
        .form-section {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        .form-section h6 {
            color: #495057;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        .required-field {
            color: #dc3545;
        }
        .patient-status {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
        }
        .status-active { background-color: #d4edda; color: #155724; }
        .status-inactive { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-brain me-2"></i>NeuroScan
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">Dr. {{ doctor.full_name }}</span>
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-arrow-left me-1"></i>Tableau de bord
                </a>
            </div>
        </div>
    </nav>

    <!-- En-tête -->
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-users-cog me-3"></i>Gestion des Patients
                    </h1>
                    <p class="mb-0 fs-5">Ajouter, modifier et supprimer les informations des patients</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light btn-lg" onclick="showAddPatientModal()">
                        <i class="fas fa-plus me-2"></i>Nouveau Patient
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Statistiques rapides -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-users fa-2x text-primary mb-2"></i>
                        <h5 class="card-title">Total Patients</h5>
                        <h3 class="text-primary" id="totalPatients">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-user-plus fa-2x text-success mb-2"></i>
                        <h5 class="card-title">Nouveaux ce Mois</h5>
                        <h3 class="text-success" id="newPatients">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-chart-line fa-2x text-info mb-2"></i>
                        <h5 class="card-title">Avec Analyses</h5>
                        <h3 class="text-info" id="patientsWithAnalyses">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                        <h5 class="card-title">Dernière Mise à Jour</h5>
                        <h3 class="text-warning" id="lastUpdate">-</h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtres et recherche -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="searchInput" placeholder="Rechercher un patient...">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="genderFilter">
                                    <option value="">Tous les genres</option>
                                    <option value="M">Masculin</option>
                                    <option value="F">Féminin</option>
                                    <option value="Autre">Autre</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="sortBy">
                                    <option value="patient_name">Nom</option>
                                    <option value="patient_id">ID Patient</option>
                                    <option value="created_at">Date de création</option>
                                    <option value="updated_at">Dernière modification</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <div class="btn-group w-100" role="group">
                                    <button class="btn btn-outline-primary" onclick="refreshPatients()">
                                        <i class="fas fa-sync-alt me-1"></i>Actualiser
                                    </button>
                                    <button class="btn btn-outline-success" onclick="exportPatients()">
                                        <i class="fas fa-download me-1"></i>Exporter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table des patients -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>Liste des Patients
                            <span class="badge bg-primary ms-2" id="patientsCount">0</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="patientsTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID Patient</th>
                                        <th>Nom</th>
                                        <th>Genre</th>
                                        <th>Date de Naissance</th>
                                        <th>Téléphone</th>
                                        <th>Analyses</th>
                                        <th>Dernière Modification</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="patientsTableBody">
                                    <!-- Données chargées dynamiquement -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour ajouter/modifier un patient -->
    <div class="modal fade" id="patientModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="patientModalTitle">Nouveau Patient</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="patientForm">
                        <!-- Informations de base -->
                        <div class="form-section">
                            <h6><i class="fas fa-user me-2"></i>Informations de Base</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="patient_id" class="form-label">ID Patient <span class="required-field">*</span></label>
                                    <input type="text" class="form-control" id="patient_id" name="patient_id" required>
                                    <div class="form-text">Identifiant unique du patient</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="patient_name" class="form-label">Nom Complet <span class="required-field">*</span></label>
                                    <input type="text" class="form-control" id="patient_name" name="patient_name" required>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <label for="date_of_birth" class="form-label">Date de Naissance</label>
                                    <input type="date" class="form-control" id="date_of_birth" name="date_of_birth">
                                </div>
                                <div class="col-md-4">
                                    <label for="gender" class="form-label">Genre</label>
                                    <select class="form-select" id="gender" name="gender">
                                        <option value="">Sélectionner...</option>
                                        <option value="M">Masculin</option>
                                        <option value="F">Féminin</option>
                                        <option value="Autre">Autre</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="insurance_number" class="form-label">Numéro d'Assurance</label>
                                    <input type="text" class="form-control" id="insurance_number" name="insurance_number">
                                </div>
                            </div>
                        </div>

                        <!-- Informations de contact -->
                        <div class="form-section">
                            <h6><i class="fas fa-address-book me-2"></i>Informations de Contact</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">Téléphone</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-12">
                                    <label for="address" class="form-label">Adresse</label>
                                    <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Contact d'urgence -->
                        <div class="form-section">
                            <h6><i class="fas fa-phone-alt me-2"></i>Contact d'Urgence</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="emergency_contact_name" class="form-label">Nom du Contact</label>
                                    <input type="text" class="form-control" id="emergency_contact_name" name="emergency_contact_name">
                                </div>
                                <div class="col-md-6">
                                    <label for="emergency_contact_phone" class="form-label">Téléphone d'Urgence</label>
                                    <input type="tel" class="form-control" id="emergency_contact_phone" name="emergency_contact_phone">
                                </div>
                            </div>
                        </div>

                        <!-- Informations médicales -->
                        <div class="form-section">
                            <h6><i class="fas fa-stethoscope me-2"></i>Informations Médicales</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="medical_history" class="form-label">Antécédents Médicaux</label>
                                    <textarea class="form-control" id="medical_history" name="medical_history" rows="3"></textarea>
                                </div>
                                <div class="col-md-6">
                                    <label for="allergies" class="form-label">Allergies</label>
                                    <textarea class="form-control" id="allergies" name="allergies" rows="3"></textarea>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <label for="current_medications" class="form-label">Médicaments Actuels</label>
                                    <textarea class="form-control" id="current_medications" name="current_medications" rows="3"></textarea>
                                </div>
                                <div class="col-md-6">
                                    <label for="notes" class="form-label">Notes Additionnelles</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="savePatient()">
                        <i class="fas fa-save me-1"></i>Enregistrer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de confirmation de suppression -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">Confirmer la Suppression</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                        <h5>Attention !</h5>
                        <p>Êtes-vous sûr de vouloir supprimer ce patient ?</p>
                        <p class="text-muted">
                            <strong id="patientToDelete"></strong><br>
                            Cette action supprimera définitivement :
                        </p>
                        <ul class="text-start text-muted">
                            <li>Toutes les informations du patient</li>
                            <li>Toutes les analyses associées</li>
                            <li>Tout l'historique d'évolution</li>
                            <li>Toutes les alertes médicales</li>
                        </ul>
                        <p class="text-danger"><strong>Cette action est irréversible !</strong></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDeletePatient()">
                        <i class="fas fa-trash me-1"></i>Supprimer Définitivement
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de détails patient -->
    <div class="modal fade" id="detailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Détails du Patient</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="patientDetailsContent">
                    <!-- Contenu chargé dynamiquement -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <button type="button" class="btn btn-primary" onclick="editPatientFromDetails()">
                        <i class="fas fa-edit me-1"></i>Modifier
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script>
        let allPatients = [];
        let currentPatient = null;
        let patientToDeleteId = null;
        let isEditMode = false;

        document.addEventListener('DOMContentLoaded', function() {
            loadPatients();
            setupEventListeners();
        });

        function setupEventListeners() {
            document.getElementById('searchInput').addEventListener('input', filterPatients);
            document.getElementById('genderFilter').addEventListener('change', filterPatients);
            document.getElementById('sortBy').addEventListener('change', sortPatients);
        }

        async function loadPatients() {
            try {
                const response = await fetch('/api/my-patients');
                const data = await response.json();

                if (data.success) {
                    allPatients = data.data;
                    displayPatients();
                    updateStats();
                } else {
                    showToast('Erreur lors du chargement des patients: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Erreur lors du chargement des patients:', error);
                showToast('Erreur lors du chargement des patients', 'error');
            }
        }

        function displayPatients() {
            const tbody = document.getElementById('patientsTableBody');

            if (allPatients.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="8" class="text-center text-muted py-4">
                            <i class="fas fa-users fa-3x mb-3"></i>
                            <h5>Aucun patient trouvé</h5>
                            <p>Commencez par ajouter votre premier patient</p>
                            <button class="btn btn-primary" onclick="showAddPatientModal()">
                                <i class="fas fa-plus me-1"></i>Ajouter un Patient
                            </button>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = allPatients.map(patient => `
                <tr>
                    <td><strong>${patient.patient_id}</strong></td>
                    <td>${patient.patient_name || 'Non renseigné'}</td>
                    <td>
                        ${patient.gender ?
                            `<span class="badge bg-${patient.gender === 'M' ? 'primary' : patient.gender === 'F' ? 'danger' : 'secondary'}">${getGenderText(patient.gender)}</span>` :
                            '<span class="text-muted">-</span>'
                        }
                    </td>
                    <td>${patient.date_of_birth ? formatDate(patient.date_of_birth) : '<span class="text-muted">-</span>'}</td>
                    <td>${patient.phone || '<span class="text-muted">-</span>'}</td>
                    <td>
                        <span class="badge bg-info">${patient.total_analyses || 0}</span>
                    </td>
                    <td>${patient.updated_at ? formatDateTime(patient.updated_at) : formatDateTime(patient.created_at)}</td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline-info" onclick="showPatientDetails('${patient.patient_id}')" title="Voir détails">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="editPatient('${patient.patient_id}')" title="Modifier">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="viewPatientProfile('${patient.patient_id}')" title="Profil complet">
                                <i class="fas fa-user"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="showDeleteModal('${patient.patient_id}', '${patient.patient_name}')" title="Supprimer">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');

            document.getElementById('patientsCount').textContent = allPatients.length;
        }

        function updateStats() {
            document.getElementById('totalPatients').textContent = allPatients.length;

            // Nouveaux patients ce mois
            const thisMonth = new Date();
            thisMonth.setDate(1);
            const newThisMonth = allPatients.filter(p =>
                new Date(p.created_at) >= thisMonth
            ).length;
            document.getElementById('newPatients').textContent = newThisMonth;

            // Patients avec analyses
            const withAnalyses = allPatients.filter(p => p.total_analyses > 0).length;
            document.getElementById('patientsWithAnalyses').textContent = withAnalyses;

            // Dernière mise à jour
            if (allPatients.length > 0) {
                const lastUpdate = allPatients.reduce((latest, patient) => {
                    const patientDate = new Date(patient.updated_at || patient.created_at);
                    return patientDate > latest ? patientDate : latest;
                }, new Date(0));

                document.getElementById('lastUpdate').textContent = formatRelativeTime(lastUpdate);
            }
        }

        function showAddPatientModal() {
            isEditMode = false;
            currentPatient = null;
            document.getElementById('patientModalTitle').textContent = 'Nouveau Patient';
            document.getElementById('patientForm').reset();
            document.getElementById('patient_id').disabled = false;
            new bootstrap.Modal(document.getElementById('patientModal')).show();
        }

        async function editPatient(patientId) {
            try {
                const response = await fetch(`/api/patients/${patientId}/details`);
                const data = await response.json();

                if (data.success) {
                    isEditMode = true;
                    currentPatient = data.data;
                    document.getElementById('patientModalTitle').textContent = 'Modifier Patient';

                    // Remplir le formulaire
                    Object.keys(data.data).forEach(key => {
                        const element = document.getElementById(key);
                        if (element) {
                            element.value = data.data[key] || '';
                        }
                    });

                    document.getElementById('patient_id').disabled = true;
                    new bootstrap.Modal(document.getElementById('patientModal')).show();
                } else {
                    showToast('Erreur lors du chargement des détails: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Erreur lors du chargement des détails:', error);
                showToast('Erreur lors du chargement des détails', 'error');
            }
        }

        async function savePatient() {
            const form = document.getElementById('patientForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());

            // Validation
            if (!data.patient_id || !data.patient_name) {
                showToast('Veuillez remplir les champs obligatoires', 'error');
                return;
            }

            try {
                const url = isEditMode ? `/api/patients/${currentPatient.patient_id}` : '/api/patients';
                const method = isEditMode ? 'PUT' : 'POST';

                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showToast(result.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('patientModal')).hide();
                    loadPatients(); // Recharger la liste
                } else {
                    showToast('Erreur: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('Erreur lors de la sauvegarde:', error);
                showToast('Erreur lors de la sauvegarde', 'error');
            }
        }

        function showDeleteModal(patientId, patientName) {
            patientToDeleteId = patientId;
            document.getElementById('patientToDelete').textContent = `${patientName} (${patientId})`;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        async function confirmDeletePatient() {
            if (!patientToDeleteId) return;

            try {
                const response = await fetch(`/api/patients/${patientToDeleteId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showToast(result.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                    loadPatients(); // Recharger la liste
                } else {
                    showToast('Erreur: ' + result.error, 'error');
                }
            } catch (error) {
                console.error('Erreur lors de la suppression:', error);
                showToast('Erreur lors de la suppression', 'error');
            }
        }

        // Fonctions utilitaires
        function getGenderText(gender) {
            switch(gender) {
                case 'M': return 'Masculin';
                case 'F': return 'Féminin';
                case 'Autre': return 'Autre';
                default: return '';
            }
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleDateString('fr-FR');
        }

        function formatDateTime(dateString) {
            if (!dateString) return '';
            return new Date(dateString).toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function formatRelativeTime(date) {
            const now = new Date();
            const diffMs = now - date;
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

            if (diffDays === 0) return 'Aujourd\'hui';
            if (diffDays === 1) return 'Hier';
            if (diffDays < 7) return `Il y a ${diffDays} jours`;
            if (diffDays < 30) return `Il y a ${Math.floor(diffDays / 7)} semaines`;
            return `Il y a ${Math.floor(diffDays / 30)} mois`;
        }

        function showToast(message, type) {
            const toastContainer = document.createElement('div');
            toastContainer.style.position = 'fixed';
            toastContainer.style.top = '20px';
            toastContainer.style.right = '20px';
            toastContainer.style.zIndex = '9999';

            toastContainer.innerHTML = `
                <div class="toast show" role="alert">
                    <div class="toast-header">
                        <i class="fas fa-${type === 'success' ? 'check-circle text-success' : 'exclamation-circle text-danger'} me-2"></i>
                        <strong class="me-auto">NeuroScan</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;

            document.body.appendChild(toastContainer);

            setTimeout(() => {
                toastContainer.remove();
            }, 5000);
        }

        function viewPatientProfile(patientId) {
            window.location.href = `/patient/${patientId}`;
        }

        function filterPatients() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const genderFilter = document.getElementById('genderFilter').value;

            const filtered = allPatients.filter(patient => {
                const matchesSearch = patient.patient_name?.toLowerCase().includes(searchTerm) ||
                                    patient.patient_id.toLowerCase().includes(searchTerm) ||
                                    patient.phone?.toLowerCase().includes(searchTerm) ||
                                    patient.email?.toLowerCase().includes(searchTerm);

                const matchesGender = !genderFilter || patient.gender === genderFilter;

                return matchesSearch && matchesGender;
            });

            // Temporairement remplacer allPatients pour l'affichage
            const originalPatients = allPatients;
            allPatients = filtered;
            displayPatients();
            allPatients = originalPatients;
        }

        function sortPatients() {
            const sortBy = document.getElementById('sortBy').value;

            allPatients.sort((a, b) => {
                let aVal = a[sortBy] || '';
                let bVal = b[sortBy] || '';

                if (sortBy.includes('date') || sortBy.includes('at')) {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                }

                return aVal > bVal ? 1 : -1;
            });

            displayPatients();
        }

        function refreshPatients() {
            loadPatients();
            showToast('Liste des patients actualisée', 'success');
        }

        function exportPatients() {
            const csvContent = "data:text/csv;charset=utf-8," +
                "ID Patient,Nom,Genre,Date de Naissance,Téléphone,Email,Analyses,Créé le\n" +
                allPatients.map(p =>
                    `"${p.patient_id}","${p.patient_name || ''}","${getGenderText(p.gender) || ''}","${p.date_of_birth || ''}","${p.phone || ''}","${p.email || ''}","${p.total_analyses || 0}","${formatDateTime(p.created_at)}"`
                ).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `patients_${new Date().toISOString().split('T')[0]}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showToast('Export terminé', 'success');
        }

        async function showPatientDetails(patientId) {
            try {
                const response = await fetch(`/api/patients/${patientId}/details`);
                const data = await response.json();

                if (data.success) {
                    const patient = data.data;
                    currentPatient = patient;

                    document.getElementById('patientDetailsContent').innerHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary">Informations de Base</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>ID Patient:</strong></td><td>${patient.patient_id}</td></tr>
                                    <tr><td><strong>Nom:</strong></td><td>${patient.patient_name || 'Non renseigné'}</td></tr>
                                    <tr><td><strong>Date de naissance:</strong></td><td>${patient.date_of_birth ? formatDate(patient.date_of_birth) : 'Non renseignée'}</td></tr>
                                    <tr><td><strong>Genre:</strong></td><td>${patient.gender ? getGenderText(patient.gender) : 'Non renseigné'}</td></tr>
                                    <tr><td><strong>Assurance:</strong></td><td>${patient.insurance_number || 'Non renseignée'}</td></tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-primary">Contact</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>Téléphone:</strong></td><td>${patient.phone || 'Non renseigné'}</td></tr>
                                    <tr><td><strong>Email:</strong></td><td>${patient.email || 'Non renseigné'}</td></tr>
                                    <tr><td><strong>Adresse:</strong></td><td>${patient.address || 'Non renseignée'}</td></tr>
                                </table>
                                <h6 class="text-primary mt-3">Contact d'Urgence</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>Nom:</strong></td><td>${patient.emergency_contact_name || 'Non renseigné'}</td></tr>
                                    <tr><td><strong>Téléphone:</strong></td><td>${patient.emergency_contact_phone || 'Non renseigné'}</td></tr>
                                </table>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6 class="text-primary">Informations Médicales</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Antécédents:</strong><br>
                                        <p class="text-muted">${patient.medical_history || 'Aucun antécédent renseigné'}</p>
                                        <strong>Allergies:</strong><br>
                                        <p class="text-muted">${patient.allergies || 'Aucune allergie renseignée'}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Médicaments actuels:</strong><br>
                                        <p class="text-muted">${patient.current_medications || 'Aucun médicament renseigné'}</p>
                                        <strong>Notes:</strong><br>
                                        <p class="text-muted">${patient.notes || 'Aucune note'}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6 class="text-primary">Statistiques</h6>
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="border rounded p-2">
                                            <h5 class="text-info">${patient.total_analyses || 0}</h5>
                                            <small>Analyses</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="border rounded p-2">
                                            <h5 class="text-success">${patient.first_analysis_date ? formatDate(patient.first_analysis_date) : 'Aucune'}</h5>
                                            <small>Première analyse</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="border rounded p-2">
                                            <h5 class="text-warning">${patient.last_analysis_date ? formatDate(patient.last_analysis_date) : 'Aucune'}</h5>
                                            <small>Dernière analyse</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="border rounded p-2">
                                            <h5 class="text-secondary">${formatDateTime(patient.created_at)}</h5>
                                            <small>Créé le</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    new bootstrap.Modal(document.getElementById('detailsModal')).show();
                } else {
                    showToast('Erreur lors du chargement des détails: ' + data.error, 'error');
                }
            } catch (error) {
                console.error('Erreur lors du chargement des détails:', error);
                showToast('Erreur lors du chargement des détails', 'error');
            }
        }

        function editPatientFromDetails() {
            bootstrap.Modal.getInstance(document.getElementById('detailsModal')).hide();
            setTimeout(() => editPatient(currentPatient.patient_id), 300);
        }
    </script>
</body>
</html>
