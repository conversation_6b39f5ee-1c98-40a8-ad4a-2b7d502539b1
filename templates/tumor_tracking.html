<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suivi de l'Évolution des Tumeurs - NeuroScan AI</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Date adapter for Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .patient-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .patient-card:hover {
            border-color: var(--secondary-color);
        }

        .evolution-badge {
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
        }

        .evolution-stable { background-color: var(--success-color); }
        .evolution-improvement { background-color: var(--success-color); }
        .evolution-degradation { background-color: var(--danger-color); }
        .evolution-growth { background-color: var(--warning-color); }
        .evolution-reduction { background-color: var(--success-color); }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .comparison-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }

        .stats-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .alert-card {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color), #2980b9);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .modal-content {
            border-radius: 15px;
            border: none;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }

        .table th {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }

        .confidence-bar {
            height: 20px;
            border-radius: 10px;
            background: linear-gradient(90deg, #ff6b6b 0%, #feca57 50%, #48dbfb 100%);
            position: relative;
            overflow: hidden;
        }

        .confidence-indicator {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background: rgba(255,255,255,0.3);
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        @media (max-width: 768px) {
            .chart-container {
                height: 300px;
            }
            
            .card {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-brain me-2"></i>
                NeuroScan AI - Suivi Tumeurs
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="fas fa-home me-1"></i>Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/pro-dashboard"><i class="fas fa-chart-bar me-1"></i>Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/tumor-tracking"><i class="fas fa-chart-line me-1"></i>Suivi Tumeurs</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Header Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            Suivi de l'Évolution des Tumeurs Cérébrales
                        </h3>
                    </div>
                    <div class="card-body">
                        <p class="mb-0">
                            Suivez l'évolution des tumeurs cérébrales de vos patients à travers le temps.
                            Comparez les diagnostics, analysez les tendances et identifiez les changements significatifs.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4" id="summaryCards">
            <div class="col-md-3">
                <div class="card stats-card text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4 id="totalPatients">-</h4>
                        <p class="mb-0">Patients Suivis</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card comparison-card text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                        <h4 id="recentEvolutions">-</h4>
                        <p class="mb-0">Évolutions Récentes</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card alert-card text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <h4 id="criticalAlerts">-</h4>
                        <p class="mb-0">Alertes Critiques</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h4 id="avgInterval">-</h4>
                        <p class="mb-0">Intervalle Moyen</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Patients List and Evolution Charts -->
        <div class="row">
            <!-- Patients List -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            Liste des Patients
                        </h5>
                    </div>
                    <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                        <div id="patientsLoading" class="loading-spinner">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                        </div>
                        <div id="patientsList" style="display: none;"></div>
                    </div>
                </div>
            </div>

            <!-- Evolution Charts -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>
                            Évolution du Patient
                        </h5>
                        <div>
                            <button class="btn btn-sm btn-outline-light me-2" onclick="showComparisonModal()">
                                <i class="fas fa-balance-scale me-1"></i>Comparer
                            </button>
                            <button class="btn btn-sm btn-outline-light" onclick="exportPatientData()">
                                <i class="fas fa-download me-1"></i>Exporter
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="noPatientSelected" class="text-center py-5">
                            <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Sélectionnez un patient pour voir son évolution</h5>
                            <p class="text-muted">Cliquez sur un patient dans la liste pour afficher ses graphiques d'évolution.</p>
                        </div>
                        
                        <div id="evolutionCharts" style="display: none;">
                            <!-- Patient Info -->
                            <div id="patientInfo" class="mb-4"></div>
                            
                            <!-- Charts Container -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="chart-container">
                                        <canvas id="evolutionChart"></canvas>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="chart-container">
                                        <canvas id="confidenceChart"></canvas>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="chart-container">
                                        <canvas id="sizeChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Evolution Details Table -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card" id="evolutionDetailsCard" style="display: none;">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            Détails de l'Évolution
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="evolutionTable">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Diagnostic</th>
                                        <th>Confiance</th>
                                        <th>Taille Estimée</th>
                                        <th>Changements</th>
                                        <th>Type d'Évolution</th>
                                    </tr>
                                </thead>
                                <tbody id="evolutionTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Comparison Modal -->
    <div class="modal fade" id="comparisonModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-balance-scale me-2"></i>
                        Comparaison d'Analyses
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="comparisonContent">
                    <!-- Comparison content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        let currentPatientId = null;
        let evolutionChart = null;
        let confidenceChart = null;
        let sizeChart = null;
        let patientsData = [];

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            loadSummaryData();
            loadPatientsList();
        });

        // Load summary statistics
        async function loadSummaryData() {
            try {
                const response = await fetch('/api/evolution/summary');
                const data = await response.json();

                if (data.success) {
                    document.getElementById('totalPatients').textContent = data.data.total_patients_tracked || 0;
                    document.getElementById('recentEvolutions').textContent = data.data.recent_evolutions.length || 0;
                    document.getElementById('criticalAlerts').textContent = data.data.critical_alerts.length || 0;
                    document.getElementById('avgInterval').textContent = '15j'; // Placeholder
                }
            } catch (error) {
                console.error('Erreur lors du chargement du résumé:', error);
            }
        }

        // Load patients list
        async function loadPatientsList() {
            try {
                const response = await fetch('/api/patients');
                const data = await response.json();

                if (data.success) {
                    patientsData = data.data;
                    displayPatientsList(data.data);
                }
            } catch (error) {
                console.error('Erreur lors du chargement des patients:', error);
            } finally {
                document.getElementById('patientsLoading').style.display = 'none';
                document.getElementById('patientsList').style.display = 'block';
            }
        }

        // Display patients list
        function displayPatientsList(patients) {
            const container = document.getElementById('patientsList');

            if (patients.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-user-slash fa-2x text-muted mb-2"></i>
                        <p class="text-muted">Aucun patient avec suivi disponible</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = patients.map(patient => `
                <div class="patient-card card mb-2" onclick="selectPatient('${patient.patient_id}')">
                    <div class="card-body p-3">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">${patient.patient_name || patient.patient_id}</h6>
                                <small class="text-muted">ID: ${patient.patient_id}</small>
                            </div>
                            <span class="badge bg-primary">${patient.total_analyses}</span>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                ${formatDate(patient.last_analysis_date)}
                            </small>
                        </div>
                        <div class="mt-2">
                            <span class="badge ${getDiagnosisBadgeClass(patient.last_diagnosis)}">
                                ${patient.last_diagnosis || 'N/A'}
                            </span>
                            <div class="confidence-bar mt-1">
                                <div class="confidence-indicator" style="width: ${patient.last_confidence}%"></div>
                            </div>
                            <small class="text-muted">${patient.last_confidence}% confiance</small>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Select a patient and load their evolution
        async function selectPatient(patientId) {
            currentPatientId = patientId;

            // Update UI
            document.querySelectorAll('.patient-card').forEach(card => {
                card.classList.remove('border-primary');
            });
            event.currentTarget.classList.add('border-primary');

            // Show loading
            document.getElementById('noPatientSelected').style.display = 'none';
            document.getElementById('evolutionCharts').style.display = 'block';

            try {
                const response = await fetch(`/api/patients/${patientId}/evolution`);
                const data = await response.json();

                if (data.success) {
                    displayPatientEvolution(data.data);
                }
            } catch (error) {
                console.error('Erreur lors du chargement de l\'évolution:', error);
            }
        }

        // Display patient evolution
        function displayPatientEvolution(data) {
            // Update patient info
            const patientInfo = document.getElementById('patientInfo');
            patientInfo.innerHTML = `
                <div class="row">
                    <div class="col-md-8">
                        <h5>${data.patient_id}</h5>
                        <p class="text-muted mb-0">
                            ${data.summary.total_analyses} analyses •
                            Du ${formatDate(data.summary.first_exam)} au ${formatDate(data.summary.last_exam)}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="badge ${getDiagnosisBadgeClass(data.summary.current_diagnosis)} fs-6">
                            ${data.summary.current_diagnosis}
                        </span>
                        <div class="mt-1">
                            <small class="text-muted">${data.summary.current_confidence}% confiance</small>
                        </div>
                    </div>
                </div>
            `;

            // Create charts
            createEvolutionCharts(data.analyses);

            // Update evolution table
            updateEvolutionTable(data.analyses, data.evolution_details);

            // Show evolution details card
            document.getElementById('evolutionDetailsCard').style.display = 'block';
        }

        // Create evolution charts
        function createEvolutionCharts(analyses) {
            const dates = analyses.map(a => a.exam_date);
            const diagnoses = analyses.map(a => a.predicted_label);
            const confidences = analyses.map(a => a.confidence);
            const sizes = analyses.map(a => a.tumor_size_estimate || 0);

            // Destroy existing charts
            if (evolutionChart) evolutionChart.destroy();
            if (confidenceChart) confidenceChart.destroy();
            if (sizeChart) sizeChart.destroy();

            // Evolution timeline chart
            const ctx1 = document.getElementById('evolutionChart').getContext('2d');
            evolutionChart = new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [{
                        label: 'Évolution du Diagnostic',
                        data: diagnoses.map((d, i) => ({
                            x: dates[i],
                            y: getDiagnosisValue(d),
                            diagnosis: d
                        })),
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Évolution du Diagnostic dans le Temps'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `Diagnostic: ${context.raw.diagnosis}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 4,
                            ticks: {
                                callback: function(value) {
                                    const labels = ['', 'Normal', 'Gliome', 'Méningiome', 'Tumeur pituitaire'];
                                    return labels[value] || '';
                                }
                            }
                        }
                    }
                }
            });

            // Confidence chart
            const ctx2 = document.getElementById('confidenceChart').getContext('2d');
            confidenceChart = new Chart(ctx2, {
                type: 'line',
                data: {
                    labels: dates,
                    datasets: [{
                        label: 'Confiance (%)',
                        data: confidences,
                        borderColor: '#27ae60',
                        backgroundColor: 'rgba(39, 174, 96, 0.1)',
                        borderWidth: 2,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Évolution de la Confiance'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });

            // Size chart
            const ctx3 = document.getElementById('sizeChart').getContext('2d');
            sizeChart = new Chart(ctx3, {
                type: 'bar',
                data: {
                    labels: dates,
                    datasets: [{
                        label: 'Taille Estimée (cm)',
                        data: sizes,
                        backgroundColor: 'rgba(241, 196, 15, 0.7)',
                        borderColor: '#f1c40f',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Évolution de la Taille Estimée'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value + ' cm';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Update evolution table
        function updateEvolutionTable(analyses, evolutionDetails) {
            const tbody = document.getElementById('evolutionTableBody');

            tbody.innerHTML = analyses.map((analysis, index) => {
                const evolution = evolutionDetails.find(e => e.exam_date === analysis.exam_date);
                const prevAnalysis = index > 0 ? analyses[index - 1] : null;

                let changes = [];
                if (prevAnalysis) {
                    if (analysis.predicted_label !== prevAnalysis.predicted_label) {
                        changes.push(`Diagnostic: ${prevAnalysis.predicted_label} → ${analysis.predicted_label}`);
                    }
                    const confChange = analysis.confidence - prevAnalysis.confidence;
                    if (Math.abs(confChange) > 5) {
                        changes.push(`Confiance: ${confChange > 0 ? '+' : ''}${confChange.toFixed(1)}%`);
                    }
                }

                return `
                    <tr>
                        <td>${formatDate(analysis.exam_date)}</td>
                        <td>
                            <span class="badge ${getDiagnosisBadgeClass(analysis.predicted_label)}">
                                ${analysis.predicted_label}
                            </span>
                        </td>
                        <td>${analysis.confidence}%</td>
                        <td>${analysis.tumor_size_estimate ? analysis.tumor_size_estimate + ' cm' : 'N/A'}</td>
                        <td><small>${changes.join('<br>')}</small></td>
                        <td>
                            ${evolution ? `<span class="evolution-badge evolution-${evolution.evolution_type}">${evolution.evolution_type}</span>` : ''}
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Show comparison modal
        async function showComparisonModal() {
            if (!currentPatientId) {
                alert('Veuillez sélectionner un patient d\'abord');
                return;
            }

            try {
                const response = await fetch(`/api/patients/${currentPatientId}/comparison`);
                const data = await response.json();

                if (data.success) {
                    displayComparison(data.data);
                    new bootstrap.Modal(document.getElementById('comparisonModal')).show();
                } else {
                    alert(data.error || 'Erreur lors de la comparaison');
                }
            } catch (error) {
                console.error('Erreur lors de la comparaison:', error);
                alert('Erreur lors de la comparaison');
            }
        }

        // Display comparison data
        function displayComparison(comparison) {
            const content = document.getElementById('comparisonContent');

            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Analyse Précédente</h6>
                        <div class="card">
                            <div class="card-body">
                                <p><strong>Date:</strong> ${formatDate(comparison.previous_analysis.exam_date)}</p>
                                <p><strong>Diagnostic:</strong>
                                    <span class="badge ${getDiagnosisBadgeClass(comparison.previous_analysis.diagnosis)}">
                                        ${comparison.previous_analysis.diagnosis}
                                    </span>
                                </p>
                                <p><strong>Confiance:</strong> ${comparison.previous_analysis.confidence}%</p>
                                <p><strong>Taille:</strong> ${comparison.previous_analysis.tumor_size || 'N/A'}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Analyse Actuelle</h6>
                        <div class="card">
                            <div class="card-body">
                                <p><strong>Date:</strong> ${formatDate(comparison.current_analysis.exam_date)}</p>
                                <p><strong>Diagnostic:</strong>
                                    <span class="badge ${getDiagnosisBadgeClass(comparison.current_analysis.diagnosis)}">
                                        ${comparison.current_analysis.diagnosis}
                                    </span>
                                </p>
                                <p><strong>Confiance:</strong> ${comparison.current_analysis.confidence}%</p>
                                <p><strong>Taille:</strong> ${comparison.current_analysis.tumor_size || 'N/A'}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h6>Changements Détectés</h6>
                    <div class="card">
                        <div class="card-body">
                            ${comparison.changes.diagnosis_changed ?
                                `<p><i class="fas fa-exchange-alt text-warning me-2"></i><strong>Changement de diagnostic:</strong> ${comparison.changes.diagnosis_change}</p>` :
                                '<p><i class="fas fa-check text-success me-2"></i>Diagnostic stable</p>'
                            }
                            <p><i class="fas fa-chart-line me-2"></i><strong>Variation de confiance:</strong>
                                <span class="${comparison.changes.confidence_change > 0 ? 'text-success' : 'text-danger'}">
                                    ${comparison.changes.confidence_change > 0 ? '+' : ''}${comparison.changes.confidence_change}%
                                </span>
                            </p>
                            ${comparison.changes.size_change ?
                                `<p><i class="fas fa-ruler me-2"></i><strong>Variation de taille:</strong>
                                    <span class="${comparison.changes.size_change > 0 ? 'text-warning' : 'text-success'}">
                                        ${comparison.changes.size_change > 0 ? '+' : ''}${comparison.changes.size_change} cm
                                    </span>
                                </p>` : ''
                            }
                            <p><i class="fas fa-calendar me-2"></i><strong>Intervalle:</strong> ${comparison.changes.time_interval_days} jours</p>
                        </div>
                    </div>
                </div>
            `;
        }

        // Export patient data
        function exportPatientData() {
            if (!currentPatientId) {
                alert('Veuillez sélectionner un patient d\'abord');
                return;
            }

            // Create export URL
            const exportUrl = `/api/patients/${currentPatientId}/export`;
            window.open(exportUrl, '_blank');
        }

        // Utility functions
        function formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR');
        }

        function getDiagnosisBadgeClass(diagnosis) {
            const classes = {
                'Normal': 'bg-success',
                'Gliome': 'bg-danger',
                'Méningiome': 'bg-warning',
                'Tumeur pituitaire': 'bg-info'
            };
            return classes[diagnosis] || 'bg-secondary';
        }

        function getDiagnosisValue(diagnosis) {
            const values = {
                'Normal': 1,
                'Gliome': 2,
                'Méningiome': 3,
                'Tumeur pituitaire': 4
            };
            return values[diagnosis] || 0;
        }
    </script>
</body>
</html>
