<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alertes Médicales | NeuroScan</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .alert-card {
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }
        .alert-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .alert-high { border-left: 4px solid #dc3545; }
        .alert-medium { border-left: 4px solid #ffc107; }
        .alert-low { border-left: 4px solid #17a2b8; }
        .alert-unread { background-color: #f8f9fa; }
        .severity-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
        }
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .stats-card {
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-brain me-2"></i>NeuroScan
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">Dr. {{ doctor.full_name }}</span>
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-arrow-left me-1"></i>Tableau de bord
                </a>
            </div>
        </div>
    </nav>

    <!-- En-tête -->
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-bell me-3"></i>Alertes Médicales
                    </h1>
                    <p class="mb-0 fs-5">Surveillance et gestion des alertes automatiques</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-light" onclick="markAllAsRead()">
                        <i class="fas fa-check-double me-1"></i>Tout marquer comme lu
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Statistiques des alertes -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                        <h5 class="card-title">Critiques</h5>
                        <h3 class="text-danger" id="criticalCount">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-exclamation-circle fa-2x text-warning mb-2"></i>
                        <h5 class="card-title">Moyennes</h5>
                        <h3 class="text-warning" id="mediumCount">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-info-circle fa-2x text-info mb-2"></i>
                        <h5 class="card-title">Faibles</h5>
                        <h3 class="text-info" id="lowCount">-</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-envelope-open fa-2x text-secondary mb-2"></i>
                        <h5 class="card-title">Non Lues</h5>
                        <h3 class="text-secondary" id="unreadCount">-</h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtres -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <label class="form-label">Filtrer par sévérité:</label>
                                <select class="form-select" id="severityFilter">
                                    <option value="">Toutes les sévérités</option>
                                    <option value="high">Critique</option>
                                    <option value="medium">Moyenne</option>
                                    <option value="low">Faible</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Filtrer par type:</label>
                                <select class="form-select" id="typeFilter">
                                    <option value="">Tous les types</option>
                                    <option value="new_tumor_detected">Nouvelle tumeur</option>
                                    <option value="diagnosis_change">Changement diagnostic</option>
                                    <option value="rapid_growth">Croissance rapide</option>
                                    <option value="confidence_drop">Baisse confiance</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Statut:</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">Toutes</option>
                                    <option value="unread">Non lues</option>
                                    <option value="read">Lues</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button class="btn btn-primary" onclick="applyFilters()">
                                        <i class="fas fa-filter me-1"></i>Appliquer
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Liste des alertes -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Alertes Actives
                            <span class="badge bg-primary ms-2" id="totalAlertsCount">0</span>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div id="alertsList">
                            <!-- Alertes chargées dynamiquement -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <div class="row mt-4">
            <div class="col-12">
                <nav aria-label="Navigation des alertes">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination dynamique -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Modal pour détails d'alerte -->
    <div class="modal fade" id="alertModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Détails de l'Alerte</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="alertModalBody">
                    <!-- Contenu dynamique -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <button type="button" class="btn btn-success" id="resolveAlertBtn">
                        <i class="fas fa-check me-1"></i>Résoudre
                    </button>
                    <button type="button" class="btn btn-primary" id="viewPatientBtn">
                        <i class="fas fa-user me-1"></i>Voir le patient
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let allAlerts = [];
        let filteredAlerts = [];
        let currentPage = 1;
        const alertsPerPage = 10;
        let currentAlert = null;

        document.addEventListener('DOMContentLoaded', function() {
            loadAlerts();
            setupEventListeners();
        });

        function setupEventListeners() {
            document.getElementById('resolveAlertBtn').addEventListener('click', function() {
                if (currentAlert) {
                    resolveAlert(currentAlert.id);
                }
            });

            document.getElementById('viewPatientBtn').addEventListener('click', function() {
                if (currentAlert) {
                    window.location.href = `/patient/${currentAlert.patient_id}`;
                }
            });
        }

        async function loadAlerts() {
            try {
                const response = await fetch('/api/alerts');
                const data = await response.json();

                if (data.success) {
                    allAlerts = data.data;
                    filteredAlerts = [...allAlerts];
                    displayAlerts();
                    updateStats();
                } else {
                    console.error('Erreur lors du chargement des alertes:', data.error);
                }
            } catch (error) {
                console.error('Erreur lors du chargement des alertes:', error);
            }
        }

        function updateStats() {
            const criticalCount = allAlerts.filter(a => a.severity === 'high').length;
            const mediumCount = allAlerts.filter(a => a.severity === 'medium').length;
            const lowCount = allAlerts.filter(a => a.severity === 'low').length;
            const unreadCount = allAlerts.filter(a => !a.is_read).length;

            document.getElementById('criticalCount').textContent = criticalCount;
            document.getElementById('mediumCount').textContent = mediumCount;
            document.getElementById('lowCount').textContent = lowCount;
            document.getElementById('unreadCount').textContent = unreadCount;
            document.getElementById('totalAlertsCount').textContent = filteredAlerts.length;
        }

        function displayAlerts() {
            const alertsList = document.getElementById('alertsList');
            const startIndex = (currentPage - 1) * alertsPerPage;
            const endIndex = startIndex + alertsPerPage;
            const alertsToShow = filteredAlerts.slice(startIndex, endIndex);

            if (alertsToShow.length === 0) {
                alertsList.innerHTML = `
                    <div class="p-4 text-center text-muted">
                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                        <h5>Aucune alerte</h5>
                        <p>Toutes vos alertes ont été traitées ou aucune alerte ne correspond aux filtres.</p>
                    </div>
                `;
                return;
            }

            alertsList.innerHTML = alertsToShow.map(alert => `
                <div class="alert-card alert-${alert.severity} ${!alert.is_read ? 'alert-unread' : ''} p-3 border-bottom" 
                     onclick="showAlertDetails(${alert.id})">
                    <div class="d-flex align-items-start">
                        <div class="flex-shrink-0 me-3">
                            <i class="fas fa-${getSeverityIcon(alert.severity)} text-${getSeverityColor(alert.severity)} fa-lg"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="mb-0">${alert.title}</h6>
                                <div class="d-flex align-items-center">
                                    <span class="severity-badge bg-${getSeverityColor(alert.severity)} text-white me-2">
                                        ${getSeverityText(alert.severity)}
                                    </span>
                                    ${!alert.is_read ? '<div class="bg-primary rounded-circle" style="width: 8px; height: 8px;"></div>' : ''}
                                </div>
                            </div>
                            <p class="text-muted mb-2">${alert.message}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-user me-1"></i>${alert.patient_name} (${alert.patient_id})
                                </small>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>${formatDate(alert.created_at)}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            updatePagination();
        }

        function applyFilters() {
            const severityFilter = document.getElementById('severityFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            filteredAlerts = allAlerts.filter(alert => {
                const matchesSeverity = !severityFilter || alert.severity === severityFilter;
                const matchesType = !typeFilter || alert.alert_type === typeFilter;
                const matchesStatus = !statusFilter ||
                    (statusFilter === 'unread' && !alert.is_read) ||
                    (statusFilter === 'read' && alert.is_read);

                return matchesSeverity && matchesType && matchesStatus;
            });

            currentPage = 1;
            displayAlerts();
            updateStats();
        }

        function updatePagination() {
            const totalPages = Math.ceil(filteredAlerts.length / alertsPerPage);
            const pagination = document.getElementById('pagination');

            if (totalPages <= 1) {
                pagination.innerHTML = '';
                return;
            }

            let paginationHTML = '';

            // Bouton précédent
            paginationHTML += `
                <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Précédent</a>
                </li>
            `;

            // Pages
            for (let i = 1; i <= totalPages; i++) {
                if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
                    paginationHTML += `
                        <li class="page-item ${i === currentPage ? 'active' : ''}">
                            <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                        </li>
                    `;
                } else if (i === currentPage - 3 || i === currentPage + 3) {
                    paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
                }
            }

            // Bouton suivant
            paginationHTML += `
                <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Suivant</a>
                </li>
            `;

            pagination.innerHTML = paginationHTML;
        }

        function changePage(page) {
            const totalPages = Math.ceil(filteredAlerts.length / alertsPerPage);
            if (page >= 1 && page <= totalPages) {
                currentPage = page;
                displayAlerts();
            }
        }

        async function showAlertDetails(alertId) {
            const alert = allAlerts.find(a => a.id === alertId);
            if (!alert) return;

            currentAlert = alert;

            // Marquer comme lue si pas encore lu
            if (!alert.is_read) {
                await markAlertAsRead(alertId);
            }

            const modalBody = document.getElementById('alertModalBody');
            modalBody.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Informations de l'Alerte</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Type:</strong></td><td>${getAlertTypeText(alert.alert_type)}</td></tr>
                            <tr><td><strong>Sévérité:</strong></td><td>
                                <span class="badge bg-${getSeverityColor(alert.severity)}">${getSeverityText(alert.severity)}</span>
                            </td></tr>
                            <tr><td><strong>Date:</strong></td><td>${formatDate(alert.created_at)}</td></tr>
                            <tr><td><strong>Statut:</strong></td><td>${alert.is_read ? 'Lue' : 'Non lue'}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Patient Concerné</h6>
                        <table class="table table-sm">
                            <tr><td><strong>Nom:</strong></td><td>${alert.patient_name}</td></tr>
                            <tr><td><strong>ID:</strong></td><td>${alert.patient_id}</td></tr>
                        </table>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Message</h6>
                        <div class="alert alert-${getSeverityColor(alert.severity)} alert-${getSeverityColor(alert.severity)}-light">
                            ${alert.message}
                        </div>
                    </div>
                </div>
            `;

            new bootstrap.Modal(document.getElementById('alertModal')).show();
        }

        async function markAlertAsRead(alertId) {
            try {
                const response = await fetch(`/api/alerts/${alertId}/mark-read`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const alert = allAlerts.find(a => a.id === alertId);
                    if (alert) {
                        alert.is_read = true;
                        displayAlerts();
                        updateStats();
                    }
                }
            } catch (error) {
                console.error('Erreur lors du marquage comme lu:', error);
            }
        }

        async function resolveAlert(alertId) {
            try {
                const response = await fetch(`/api/alerts/${alertId}/resolve`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    // Retirer l'alerte de la liste
                    allAlerts = allAlerts.filter(a => a.id !== alertId);
                    filteredAlerts = filteredAlerts.filter(a => a.id !== alertId);

                    displayAlerts();
                    updateStats();

                    bootstrap.Modal.getInstance(document.getElementById('alertModal')).hide();

                    // Afficher un message de succès
                    showToast('Alerte résolue avec succès', 'success');
                }
            } catch (error) {
                console.error('Erreur lors de la résolution:', error);
                showToast('Erreur lors de la résolution de l\'alerte', 'error');
            }
        }

        async function markAllAsRead() {
            const unreadAlerts = allAlerts.filter(a => !a.is_read);

            for (const alert of unreadAlerts) {
                await markAlertAsRead(alert.id);
            }

            showToast('Toutes les alertes ont été marquées comme lues', 'success');
        }

        function getSeverityIcon(severity) {
            switch (severity) {
                case 'high': return 'exclamation-triangle';
                case 'medium': return 'exclamation-circle';
                case 'low': return 'info-circle';
                default: return 'bell';
            }
        }

        function getSeverityColor(severity) {
            switch (severity) {
                case 'high': return 'danger';
                case 'medium': return 'warning';
                case 'low': return 'info';
                default: return 'secondary';
            }
        }

        function getSeverityText(severity) {
            switch (severity) {
                case 'high': return 'Critique';
                case 'medium': return 'Moyenne';
                case 'low': return 'Faible';
                default: return 'Inconnue';
            }
        }

        function getAlertTypeText(type) {
            switch (type) {
                case 'new_tumor_detected': return 'Nouvelle tumeur détectée';
                case 'diagnosis_change': return 'Changement de diagnostic';
                case 'rapid_growth': return 'Croissance rapide';
                case 'confidence_drop': return 'Baisse de confiance';
                case 'tumor_resolved': return 'Amélioration significative';
                case 'high_grade_tumor': return 'Tumeur de haut grade';
                default: return type;
            }
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMins / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffMins < 1) return 'À l\'instant';
            if (diffMins < 60) return `Il y a ${diffMins} min`;
            if (diffHours < 24) return `Il y a ${diffHours}h`;
            if (diffDays < 7) return `Il y a ${diffDays}j`;
            return date.toLocaleDateString('fr-FR', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function showToast(message, type) {
            // Créer un toast Bootstrap simple
            const toastContainer = document.createElement('div');
            toastContainer.style.position = 'fixed';
            toastContainer.style.top = '20px';
            toastContainer.style.right = '20px';
            toastContainer.style.zIndex = '9999';

            toastContainer.innerHTML = `
                <div class="toast show" role="alert">
                    <div class="toast-header">
                        <i class="fas fa-${type === 'success' ? 'check-circle text-success' : 'exclamation-circle text-danger'} me-2"></i>
                        <strong class="me-auto">NeuroScan</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body">
                        ${message}
                    </div>
                </div>
            `;

            document.body.appendChild(toastContainer);

            setTimeout(() => {
                toastContainer.remove();
            }, 5000);
        }
    </script>
</body>
</html>
