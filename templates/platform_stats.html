<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Statistiques Plateforme - NeuroScan</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stat-card-green {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        .stat-card-orange {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-card-purple {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        /* Styles pour les graphiques */
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        .chart-container canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100% !important;
            height: 100% !important;
        }

        /* Prévenir le déplacement des éléments */
        .grid {
            align-items: start;
        }

        .bg-white {
            min-height: fit-content;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo et titre -->
                <div class="flex items-center">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-brain text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900">NeuroScan</h1>
                            <p class="text-xs text-gray-500">Statistiques Plateforme</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="flex items-center space-x-4">
                    <a href="/dashboard" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i class="fas fa-home mr-2"></i>Dashboard
                    </a>
                    <a href="/pro-dashboard" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i class="fas fa-chart-bar mr-2"></i>Mes Stats
                    </a>
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900">Dr. {{ doctor.full_name }}</p>
                        <p class="text-xs text-gray-500">{{ doctor.specialty or 'Médecin' }}</p>
                    </div>
                    <a href="/logout" class="text-gray-500 hover:text-red-600 transition-colors" title="Déconnexion">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Contenu principal -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Titre -->
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">
                Statistiques Générales de la Plateforme
            </h2>
            <p class="text-gray-600">
                Vue d'ensemble de l'activité de tous les médecins sur NeuroScan
            </p>
        </div>

        <!-- Cartes de statistiques -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total analyses -->
            <div class="stat-card rounded-xl p-6 text-white card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Total Analyses</p>
                        <p id="totalAnalyses" class="text-3xl font-bold">-</p>
                    </div>
                    <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-microscope text-2xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total médecins -->
            <div class="stat-card-green rounded-xl p-6 text-white card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Médecins Actifs</p>
                        <p id="totalDoctors" class="text-3xl font-bold">-</p>
                    </div>
                    <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-md text-2xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total patients -->
            <div class="stat-card-orange rounded-xl p-6 text-white card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Patients Suivis</p>
                        <p id="totalPatients" class="text-3xl font-bold">-</p>
                    </div>
                    <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-2xl"></i>
                    </div>
                </div>
            </div>

            <!-- Confiance moyenne -->
            <div class="stat-card-purple rounded-xl p-6 text-white card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm font-medium">Confiance Moyenne</p>
                        <p id="avgConfidence" class="text-3xl font-bold">-%</p>
                    </div>
                    <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graphiques -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Répartition des diagnostics -->
            <div class="bg-white rounded-xl shadow-sm border p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-chart-pie mr-2 text-blue-600"></i>
                    Répartition des Diagnostics
                </h3>
                <div class="chart-container">
                    <canvas id="tumorChart"></canvas>
                </div>
            </div>

            <!-- Activité quotidienne -->
            <div class="bg-white rounded-xl shadow-sm border p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-chart-bar mr-2 text-green-600"></i>
                    Activité des 30 derniers jours
                </h3>
                <div class="chart-container">
                    <canvas id="activityChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Top médecins -->
        <div class="bg-white rounded-xl shadow-sm border p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">
                <i class="fas fa-trophy mr-2 text-yellow-600"></i>
                Top 5 des Médecins les Plus Actifs
            </h3>
            <div id="topDoctors" class="space-y-4">
                <!-- Sera rempli par JavaScript -->
            </div>
        </div>
    </main>

    <script>
        let tumorChart, activityChart;

        // Charger les données au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            // Initialiser les graphiques
            initializeCharts();

            // Charger les données
            loadPlatformStats();

            // Actualiser toutes les 30 secondes
            setInterval(loadPlatformStats, 30000);

            // Gérer le redimensionnement de la fenêtre
            let resizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(function() {
                    initializeCharts();
                    if (tumorChart) {
                        tumorChart.resize();
                    }
                    if (activityChart) {
                        activityChart.resize();
                    }
                }, 250);
            });
        });

        function initializeCharts() {
            // Initialiser les canvas avec des dimensions fixes
            const tumorCanvas = document.getElementById('tumorChart');
            const activityCanvas = document.getElementById('activityChart');

            // Définir les dimensions des canvas
            const containerWidth = tumorCanvas.parentElement.clientWidth;
            const containerHeight = 300;

            tumorCanvas.width = containerWidth;
            tumorCanvas.height = containerHeight;
            activityCanvas.width = containerWidth;
            activityCanvas.height = containerHeight;
        }

        async function loadPlatformStats() {
            try {
                const response = await fetch('/api/analytics/platform-overview');
                const result = await response.json();

                if (result.success) {
                    updateStatsCards(result.data);
                    updateCharts(result.data);
                    updateTopDoctors(result.data.top_doctors);
                }
            } catch (error) {
                console.error('Erreur lors du chargement des statistiques:', error);
            }
        }

        function updateStatsCards(data) {
            document.getElementById('totalAnalyses').textContent = data.total_analyses || 0;
            document.getElementById('totalDoctors').textContent = data.total_doctors || 0;
            document.getElementById('totalPatients').textContent = data.total_patients || 0;
            document.getElementById('avgConfidence').textContent = (data.avg_confidence || 0) + '%';
        }

        function updateCharts(data) {
            // Graphique en secteurs pour les diagnostics
            const tumorCtx = document.getElementById('tumorChart').getContext('2d');
            if (tumorChart) {
                tumorChart.destroy();
            }

            const tumorLabels = Object.keys(data.tumor_distribution || {});
            const tumorData = Object.values(data.tumor_distribution || {});

            // Vérifier s'il y a des données
            if (tumorLabels.length === 0 || tumorData.every(val => val === 0)) {
                tumorCtx.clearRect(0, 0, tumorCtx.canvas.width, tumorCtx.canvas.height);
                tumorCtx.font = '16px Inter';
                tumorCtx.fillStyle = '#6B7280';
                tumorCtx.textAlign = 'center';
                tumorCtx.fillText('Aucune donnée disponible', tumorCtx.canvas.width / 2, tumorCtx.canvas.height / 2);
                return;
            }

            tumorChart = new Chart(tumorCtx, {
                type: 'doughnut',
                data: {
                    labels: tumorLabels,
                    datasets: [{
                        data: tumorData,
                        backgroundColor: [
                            '#10B981', // Normal - Vert
                            '#EF4444', // Gliome - Rouge
                            '#F59E0B', // Méningiome - Orange
                            '#8B5CF6'  // Tumeur pituitaire - Violet
                        ],
                        borderWidth: 3,
                        borderColor: '#ffffff',
                        hoverBorderWidth: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                font: {
                                    size: 12,
                                    family: 'Inter'
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#374151',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: true
                        }
                    },
                    animation: {
                        animateRotate: true,
                        animateScale: false,
                        duration: 1000,
                        easing: 'easeOutQuart'
                    }
                }
            });

            // Graphique en barres pour l'activité
            const activityCtx = document.getElementById('activityChart').getContext('2d');
            if (activityChart) {
                activityChart.destroy();
            }

            const activityLabels = (data.daily_analyses || []).map(item => {
                const date = new Date(item[0]);
                return date.toLocaleDateString('fr-FR', { month: 'short', day: 'numeric' });
            });
            const activityData = (data.daily_analyses || []).map(item => item[1]);

            // Vérifier s'il y a des données
            if (activityLabels.length === 0 || activityData.every(val => val === 0)) {
                activityCtx.clearRect(0, 0, activityCtx.canvas.width, activityCtx.canvas.height);
                activityCtx.font = '16px Inter';
                activityCtx.fillStyle = '#6B7280';
                activityCtx.textAlign = 'center';
                activityCtx.fillText('Aucune donnée disponible', activityCtx.canvas.width / 2, activityCtx.canvas.height / 2);
                return;
            }

            activityChart = new Chart(activityCtx, {
                type: 'line',
                data: {
                    labels: activityLabels,
                    datasets: [{
                        label: 'Analyses par jour',
                        data: activityData,
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#3B82F6',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 5,
                        pointHoverRadius: 7,
                        pointHoverBackgroundColor: '#1D4ED8',
                        pointHoverBorderColor: '#ffffff',
                        pointHoverBorderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#374151',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: false,
                            callbacks: {
                                title: function(context) {
                                    return 'Date: ' + context[0].label;
                                },
                                label: function(context) {
                                    return 'Analyses: ' + context.parsed.y;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                font: {
                                    size: 11,
                                    family: 'Inter'
                                },
                                color: '#6B7280'
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                font: {
                                    size: 11,
                                    family: 'Inter'
                                },
                                color: '#6B7280',
                                stepSize: 1
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeOutQuart'
                    }
                }
            });
        }

        function updateTopDoctors(topDoctors) {
            const container = document.getElementById('topDoctors');
            container.innerHTML = '';
            
            if (!topDoctors || topDoctors.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center">Aucune donnée disponible</p>';
                return;
            }
            
            topDoctors.forEach((doctor, index) => {
                const rankColors = ['text-yellow-600', 'text-gray-500', 'text-orange-600', 'text-blue-600', 'text-purple-600'];
                const rankIcons = ['fa-trophy', 'fa-medal', 'fa-award', 'fa-star', 'fa-certificate'];
                
                const doctorElement = document.createElement('div');
                doctorElement.className = 'flex items-center justify-between p-4 bg-gray-50 rounded-lg';
                doctorElement.innerHTML = `
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center mr-4">
                            <i class="fas ${rankIcons[index]} ${rankColors[index]}"></i>
                        </div>
                        <div>
                            <p class="font-semibold text-gray-900">${doctor.name}</p>
                            <p class="text-sm text-gray-500">${doctor.analyses} analyses effectuées</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="text-2xl font-bold ${rankColors[index]}">#${index + 1}</span>
                    </div>
                `;
                container.appendChild(doctorElement);
            });
        }
    </script>
</body>
</html>
