<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NeuroScan Pro - Tableau de Bord Avancé</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-card.blue {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card.green {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .stat-card.purple {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
        .alert-warning {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
        }
        .alert-info {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        }
        .alert-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .filter-panel {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-left: 4px solid #3b82f6;
        }

        .filter-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .filter-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .filter-input {
            transition: all 0.3s ease;
            border: 2px solid #e5e7eb;
        }

        .filter-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            transform: translateY(-1px);
        }

        .filter-badge {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .filter-active {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .range-slider {
            -webkit-appearance: none;
            appearance: none;
            height: 6px;
            border-radius: 3px;
            background: #e5e7eb;
            outline: none;
        }

        .range-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .range-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-lg border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-brain text-3xl text-indigo-600 mr-3"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">NeuroScan Pro</h1>
                        <p class="text-sm text-gray-500">Tableau de bord avancé</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="toggleFilters" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors relative">
                        <i class="fas fa-filter mr-2"></i>Filtres Avancés
                        <span id="activeFiltersCount" class="hidden absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">0</span>
                    </button>
                    <div class="relative">
                        <button id="exportBtn" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                            <i class="fas fa-download mr-2"></i>Exporter
                        </button>
                        <div id="exportMenu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                            <a href="/api/analytics/export/csv" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-t-lg">
                                <i class="fas fa-file-csv mr-2 text-green-600"></i>Exporter CSV
                            </a>
                            <a href="/api/analytics/export/json" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-b-lg">
                                <i class="fas fa-file-code mr-2 text-blue-600"></i>Exporter JSON
                            </a>
                        </div>
                    </div>
                    <a href="/pro-dashboard" class="text-gray-600 hover:text-indigo-600 transition-colors">
                        <i class="fas fa-chart-simple mr-2"></i>Vue Simple
                    </a>
                    <a href="/" class="text-gray-600 hover:text-indigo-600 transition-colors">
                        <i class="fas fa-home mr-2"></i>Retour
                    </a>
                </div>
            </div>
        </div>
    </header>

    <!-- Advanced Filters Panel -->
    <div id="filtersPanel" class="hidden filter-panel border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <!-- Filter Header -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-sliders-h mr-2 text-blue-600"></i>
                        Filtres Avancés
                    </h3>
                    <p class="text-sm text-gray-600">Affinez vos recherches avec des critères précis</p>
                </div>
                <div class="flex items-center space-x-3">
                    <button id="resetFilters" class="text-gray-500 hover:text-red-600 transition-colors">
                        <i class="fas fa-undo mr-1"></i>Réinitialiser
                    </button>
                    <button id="saveFilters" class="text-blue-600 hover:text-blue-800 transition-colors">
                        <i class="fas fa-bookmark mr-1"></i>Sauvegarder
                    </button>
                </div>
            </div>

            <!-- Active Filters Display -->
            <div id="activeFilters" class="mb-4 hidden">
                <div class="flex flex-wrap gap-2">
                    <span class="text-sm text-gray-600 mr-2">Filtres actifs:</span>
                    <div id="activeFiltersList" class="flex flex-wrap gap-2"></div>
                </div>
            </div>

            <!-- Filter Cards Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">

                <!-- Date Range Filter -->
                <div class="filter-card p-4">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-calendar-alt text-blue-600 mr-2"></i>
                        <h4 class="font-medium text-gray-900">Période</h4>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-xs font-medium text-gray-600 mb-1">Date de début</label>
                            <input type="date" id="startDate" class="filter-input w-full px-3 py-2 text-sm rounded-lg">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-600 mb-1">Date de fin</label>
                            <input type="date" id="endDate" class="filter-input w-full px-3 py-2 text-sm rounded-lg">
                        </div>
                        <div class="flex space-x-2">
                            <button class="quick-date-btn text-xs px-2 py-1 bg-gray-100 hover:bg-blue-100 rounded" data-days="7">7j</button>
                            <button class="quick-date-btn text-xs px-2 py-1 bg-gray-100 hover:bg-blue-100 rounded" data-days="30">30j</button>
                            <button class="quick-date-btn text-xs px-2 py-1 bg-gray-100 hover:bg-blue-100 rounded" data-days="90">90j</button>
                        </div>
                    </div>
                </div>

                <!-- Diagnostic Type Filter -->
                <div class="filter-card p-4">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-brain text-green-600 mr-2"></i>
                        <h4 class="font-medium text-gray-900">Diagnostics</h4>
                    </div>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="diagnostic-checkbox mr-2 text-blue-600" value="Normal">
                            <span class="text-sm">Normal</span>
                            <span class="ml-auto text-xs text-gray-500" id="count-Normal">0</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="diagnostic-checkbox mr-2 text-blue-600" value="Gliome">
                            <span class="text-sm">Gliome</span>
                            <span class="ml-auto text-xs text-gray-500" id="count-Gliome">0</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="diagnostic-checkbox mr-2 text-blue-600" value="Méningiome">
                            <span class="text-sm">Méningiome</span>
                            <span class="ml-auto text-xs text-gray-500" id="count-Méningiome">0</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="diagnostic-checkbox mr-2 text-blue-600" value="Tumeur pituitaire">
                            <span class="text-sm">Tumeur pituitaire</span>
                            <span class="ml-auto text-xs text-gray-500" id="count-Tumeur pituitaire">0</span>
                        </label>
                    </div>
                </div>

                <!-- Confidence Range Filter -->
                <div class="filter-card p-4">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-chart-line text-purple-600 mr-2"></i>
                        <h4 class="font-medium text-gray-900">Confiance</h4>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-xs font-medium text-gray-600 mb-2">
                                Confiance minimale: <span id="minConfidenceValue" class="text-blue-600 font-semibold">0%</span>
                            </label>
                            <input type="range" id="minConfidence" min="0" max="100" value="0"
                                   class="range-slider w-full">
                        </div>
                        <div>
                            <label class="block text-xs font-medium text-gray-600 mb-2">
                                Confiance maximale: <span id="maxConfidenceValue" class="text-blue-600 font-semibold">100%</span>
                            </label>
                            <input type="range" id="maxConfidence" min="0" max="100" value="100"
                                   class="range-slider w-full">
                        </div>
                    </div>
                </div>

                <!-- Processing Time Filter -->
                <div class="filter-card p-4">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-clock text-orange-600 mr-2"></i>
                        <h4 class="font-medium text-gray-900">Temps de traitement</h4>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-xs font-medium text-gray-600 mb-2">
                                Temps max: <span id="maxTimeValue" class="text-orange-600 font-semibold">10s</span>
                            </label>
                            <input type="range" id="maxProcessingTime" min="0" max="10" value="10" step="0.1"
                                   class="range-slider w-full">
                        </div>
                        <div class="flex space-x-2">
                            <button class="quick-time-btn text-xs px-2 py-1 bg-gray-100 hover:bg-orange-100 rounded" data-time="1">< 1s</button>
                            <button class="quick-time-btn text-xs px-2 py-1 bg-gray-100 hover:bg-orange-100 rounded" data-time="3">< 3s</button>
                            <button class="quick-time-btn text-xs px-2 py-1 bg-gray-100 hover:bg-orange-100 rounded" data-time="5">< 5s</button>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Action Buttons -->
            <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
                <div class="text-sm text-gray-600">
                    <span id="filterResultsPreview">Cliquez sur "Appliquer" pour voir les résultats</span>
                </div>
                <div class="flex space-x-3">
                    <button id="previewFilters" class="px-4 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors">
                        <i class="fas fa-eye mr-2"></i>Aperçu
                    </button>
                    <button id="applyFilters" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search mr-2"></i>Appliquer les filtres
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Alerts Section -->
        <div id="alertsSection" class="mb-8">
            <!-- Alerts will be loaded here -->
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stat-card blue text-white p-6 rounded-2xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-blue-100 text-sm font-medium">Total Analyses</p>
                        <p id="totalAnalyses" class="text-3xl font-bold">-</p>
                        <p id="totalChange" class="text-xs text-blue-200 mt-1">-</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-full">
                        <i class="fas fa-chart-line text-2xl"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card green text-white p-6 rounded-2xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-green-100 text-sm font-medium">Confiance Moyenne</p>
                        <p id="avgConfidence" class="text-3xl font-bold">-%</p>
                        <p id="confidenceChange" class="text-xs text-green-200 mt-1">-</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-full">
                        <i class="fas fa-check-circle text-2xl"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card purple text-white p-6 rounded-2xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-purple-100 text-sm font-medium">Analyses Aujourd'hui</p>
                        <p id="todayAnalyses" class="text-3xl font-bold">-</p>
                        <p id="todayChange" class="text-xs text-purple-200 mt-1">-</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-full">
                        <i class="fas fa-calendar-day text-2xl"></i>
                    </div>
                </div>
            </div>

            <div class="stat-card text-white p-6 rounded-2xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-pink-100 text-sm font-medium">Temps Moyen</p>
                        <p id="avgProcessingTime" class="text-3xl font-bold">-s</p>
                        <p id="timeChange" class="text-xs text-pink-200 mt-1">-</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-full">
                        <i class="fas fa-clock text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Comparison Chart -->
            <div class="bg-white p-6 rounded-2xl shadow-lg card-hover">
                <h3 class="text-xl font-bold text-gray-800 mb-6">Comparaison Temporelle</h3>
                <div class="chart-container">
                    <canvas id="comparisonChart"></canvas>
                </div>
            </div>

            <!-- Performance Trends -->
            <div class="bg-white p-6 rounded-2xl shadow-lg card-hover">
                <h3 class="text-xl font-bold text-gray-800 mb-6">Tendances de Performance</h3>
                <div class="chart-container">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Filtered Results Table -->
        <div id="filteredResults" class="bg-white rounded-2xl shadow-lg card-hover">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-xl font-bold text-gray-800">Analyses Filtrées</h3>
                    <span id="filteredCount" class="text-sm text-gray-500">0 résultats</span>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date/Heure</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fichier</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Diagnostic</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Confiance</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Temps</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        </tr>
                    </thead>
                    <tbody id="filteredAnalysesTable" class="bg-white divide-y divide-gray-200">
                        <!-- Filtered data will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <script>
        let comparisonChart = null;
        let performanceChart = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeFilters();
            loadDashboardData();
            loadAlerts();
            loadComparisons();
            loadPerformanceTrends();

            // Auto-refresh every 30 seconds
            setInterval(() => {
                loadDashboardData();
                loadAlerts();
                loadPerformanceTrends();
            }, 30000);
        });

        // Advanced Filter functionality
        function initializeFilters() {
            const toggleFilters = document.getElementById('toggleFilters');
            const filtersPanel = document.getElementById('filtersPanel');
            const applyFilters = document.getElementById('applyFilters');
            const previewFilters = document.getElementById('previewFilters');
            const resetFilters = document.getElementById('resetFilters');

            // Range sliders
            const minConfidence = document.getElementById('minConfidence');
            const maxConfidence = document.getElementById('maxConfidence');
            const maxProcessingTime = document.getElementById('maxProcessingTime');
            const minConfidenceValue = document.getElementById('minConfidenceValue');
            const maxConfidenceValue = document.getElementById('maxConfidenceValue');
            const maxTimeValue = document.getElementById('maxTimeValue');

            // Toggle filters panel
            toggleFilters?.addEventListener('click', () => {
                filtersPanel.classList.toggle('hidden');
                if (!filtersPanel.classList.contains('hidden')) {
                    loadFilterCounts();
                }
            });

            // Range slider updates
            minConfidence?.addEventListener('input', (e) => {
                minConfidenceValue.textContent = e.target.value + '%';
                updateActiveFilters();
            });

            maxConfidence?.addEventListener('input', (e) => {
                maxConfidenceValue.textContent = e.target.value + '%';
                updateActiveFilters();
            });

            maxProcessingTime?.addEventListener('input', (e) => {
                maxTimeValue.textContent = e.target.value + 's';
                updateActiveFilters();
            });

            // Quick date buttons
            document.querySelectorAll('.quick-date-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const days = parseInt(e.target.dataset.days);
                    const endDate = new Date();
                    const startDate = new Date();
                    startDate.setDate(endDate.getDate() - days);

                    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
                    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
                    updateActiveFilters();
                });
            });

            // Quick time buttons
            document.querySelectorAll('.quick-time-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const time = parseFloat(e.target.dataset.time);
                    document.getElementById('maxProcessingTime').value = time;
                    maxTimeValue.textContent = time + 's';
                    updateActiveFilters();
                });
            });

            // Diagnostic checkboxes
            document.querySelectorAll('.diagnostic-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', updateActiveFilters);
            });

            // Date inputs
            document.getElementById('startDate')?.addEventListener('change', updateActiveFilters);
            document.getElementById('endDate')?.addEventListener('change', updateActiveFilters);

            // Action buttons
            applyFilters?.addEventListener('click', applyFiltersFunction);
            previewFilters?.addEventListener('click', previewFiltersFunction);
            resetFilters?.addEventListener('click', resetFiltersFunction);

            // Export menu
            const exportBtn = document.getElementById('exportBtn');
            const exportMenu = document.getElementById('exportMenu');
            
            exportBtn?.addEventListener('click', (e) => {
                e.stopPropagation();
                exportMenu.classList.toggle('hidden');
            });
            
            document.addEventListener('click', () => {
                exportMenu.classList.add('hidden');
            });
        }

        async function loadDashboardData() {
            try {
                const response = await fetch('/api/analytics/overview');
                const data = await response.json();
                
                if (data.success) {
                    updateOverviewStats(data.data);
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        async function loadAlerts() {
            try {
                const response = await fetch('/api/analytics/alerts');
                const data = await response.json();
                
                if (data.success) {
                    updateAlertsSection(data.data);
                }
            } catch (error) {
                console.error('Error loading alerts:', error);
            }
        }

        async function loadComparisons() {
            try {
                const response = await fetch('/api/analytics/comparison');
                const data = await response.json();

                if (data.success) {
                    updateComparisonChart(data.data);
                }
            } catch (error) {
                console.error('Error loading comparisons:', error);
            }
        }

        async function loadPerformanceTrends() {
            try {
                const response = await fetch('/api/analytics/performance');
                const data = await response.json();

                if (data.success) {
                    updatePerformanceChart(data.data);
                }
            } catch (error) {
                console.error('Error loading performance trends:', error);
            }
        }

        function updateOverviewStats(data) {
            document.getElementById('totalAnalyses').textContent = data.total_analyses;
            document.getElementById('avgConfidence').textContent = data.avg_confidence + '%';
            document.getElementById('avgProcessingTime').textContent = data.avg_processing_time + 's';
            
            // Calculate today's analyses
            const today = new Date().toISOString().split('T')[0];
            const todayData = data.daily_analyses.find(d => d[0] === today);
            document.getElementById('todayAnalyses').textContent = todayData ? todayData[1] : 0;
        }

        function updateAlertsSection(alerts) {
            const alertsSection = document.getElementById('alertsSection');
            
            if (alerts.length === 0) {
                alertsSection.innerHTML = '';
                return;
            }

            const alertsHTML = alerts.map(alert => `
                <div class="alert-${alert.type} text-white p-4 rounded-xl mb-4 flex items-center">
                    <div class="bg-white bg-opacity-20 p-2 rounded-full mr-4">
                        <i class="fas fa-${alert.type === 'warning' ? 'exclamation-triangle' : alert.type === 'info' ? 'info-circle' : 'check-circle'}"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-bold">${alert.title}</h4>
                        <p class="text-sm opacity-90">${alert.message}</p>
                    </div>
                    <div class="text-xs opacity-75">
                        ${new Date(alert.timestamp).toLocaleTimeString('fr-FR')}
                    </div>
                </div>
            `).join('');

            alertsSection.innerHTML = alertsHTML;
        }

        function updateComparisonChart(data) {
            const ctx = document.getElementById('comparisonChart').getContext('2d');
            
            if (comparisonChart) {
                comparisonChart.destroy();
            }

            // Prepare data for comparison
            const labels = ['Ce mois', 'Mois dernier'];
            const thisMonth = data.monthly['Ce mois']?.count || 0;
            const lastMonth = data.monthly['Mois dernier']?.count || 0;

            comparisonChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Nombre d\'analyses',
                        data: [thisMonth, lastMonth],
                        backgroundColor: ['rgba(59, 130, 246, 0.8)', 'rgba(156, 163, 175, 0.8)'],
                        borderColor: ['rgb(59, 130, 246)', 'rgb(156, 163, 175)'],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
        }

        function updatePerformanceChart(data) {
            const ctx = document.getElementById('performanceChart').getContext('2d');

            if (performanceChart) {
                performanceChart.destroy();
            }

            // Utiliser les données quotidiennes des 7 derniers jours
            const dailyTrends = data.daily_trends;

            // Si pas de données quotidiennes, utiliser les données horaires
            const chartData = dailyTrends.labels.length > 0 ? dailyTrends : data.hourly_trends;

            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: chartData.labels,
                    datasets: [
                        {
                            label: 'Confiance (%)',
                            data: chartData.confidence,
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            yAxisID: 'y',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'Temps de traitement (s)',
                            data: chartData.processing_time,
                            borderColor: 'rgb(239, 68, 68)',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            yAxisID: 'y1',
                            tension: 0.4,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: chartData.labels.length > 10 ? 'Jours' : 'Heures'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Confiance (%)'
                            },
                            min: 0,
                            max: 100
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Temps (s)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                            min: 0
                        }
                    }
                }
            });
        }

        // Update active filters display
        function updateActiveFilters() {
            const activeFilters = [];
            const activeFiltersContainer = document.getElementById('activeFilters');
            const activeFiltersList = document.getElementById('activeFiltersList');
            const activeFiltersCount = document.getElementById('activeFiltersCount');

            // Date filters
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            if (startDate || endDate) {
                activeFilters.push({
                    type: 'date',
                    label: `Période: ${startDate || '...'} → ${endDate || '...'}`,
                    remove: () => {
                        document.getElementById('startDate').value = '';
                        document.getElementById('endDate').value = '';
                        updateActiveFilters();
                    }
                });
            }

            // Diagnostic filters
            const selectedDiagnostics = Array.from(document.querySelectorAll('.diagnostic-checkbox:checked')).map(cb => cb.value);
            if (selectedDiagnostics.length > 0) {
                activeFilters.push({
                    type: 'diagnostic',
                    label: `Diagnostics: ${selectedDiagnostics.join(', ')}`,
                    remove: () => {
                        document.querySelectorAll('.diagnostic-checkbox').forEach(cb => cb.checked = false);
                        updateActiveFilters();
                    }
                });
            }

            // Confidence filters
            const minConf = document.getElementById('minConfidence').value;
            const maxConf = document.getElementById('maxConfidence').value;
            if (minConf > 0 || maxConf < 100) {
                activeFilters.push({
                    type: 'confidence',
                    label: `Confiance: ${minConf}% - ${maxConf}%`,
                    remove: () => {
                        document.getElementById('minConfidence').value = 0;
                        document.getElementById('maxConfidence').value = 100;
                        document.getElementById('minConfidenceValue').textContent = '0%';
                        document.getElementById('maxConfidenceValue').textContent = '100%';
                        updateActiveFilters();
                    }
                });
            }

            // Processing time filter
            const maxTime = document.getElementById('maxProcessingTime').value;
            if (maxTime < 10) {
                activeFilters.push({
                    type: 'time',
                    label: `Temps max: ${maxTime}s`,
                    remove: () => {
                        document.getElementById('maxProcessingTime').value = 10;
                        document.getElementById('maxTimeValue').textContent = '10s';
                        updateActiveFilters();
                    }
                });
            }

            // Update display
            if (activeFilters.length > 0) {
                activeFiltersContainer.classList.remove('hidden');
                activeFiltersCount.classList.remove('hidden');
                activeFiltersCount.textContent = activeFilters.length;

                activeFiltersList.innerHTML = activeFilters.map(filter => `
                    <span class="filter-badge flex items-center">
                        ${filter.label}
                        <button class="ml-2 hover:bg-white hover:bg-opacity-20 rounded-full p-1" onclick="(${filter.remove})()">
                            <i class="fas fa-times text-xs"></i>
                        </button>
                    </span>
                `).join('');
            } else {
                activeFiltersContainer.classList.add('hidden');
                activeFiltersCount.classList.add('hidden');
                activeFiltersList.innerHTML = '';
            }
        }

        // Load filter counts
        async function loadFilterCounts() {
            try {
                const response = await fetch('/api/analytics/filter-counts');
                const data = await response.json();

                if (data.success) {
                    Object.entries(data.data.diagnostic_counts).forEach(([diagnostic, count]) => {
                        const countElement = document.getElementById(`count-${diagnostic}`);
                        if (countElement) {
                            countElement.textContent = count;
                        }
                    });
                }
            } catch (error) {
                console.error('Error loading filter counts:', error);
            }
        }

        // Preview filters function
        async function previewFiltersFunction() {
            const filters = getFilterValues();

            try {
                const response = await fetch('/api/analytics/filter-preview', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(filters)
                });

                const data = await response.json();

                if (data.success) {
                    document.getElementById('filterResultsPreview').textContent =
                        `Aperçu: ${data.data.count} résultats trouvés`;
                }
            } catch (error) {
                console.error('Error previewing filters:', error);
            }
        }

        // Reset filters function
        function resetFiltersFunction() {
            document.getElementById('startDate').value = '';
            document.getElementById('endDate').value = '';
            document.querySelectorAll('.diagnostic-checkbox').forEach(cb => cb.checked = false);
            document.getElementById('minConfidence').value = 0;
            document.getElementById('maxConfidence').value = 100;
            document.getElementById('maxProcessingTime').value = 10;
            document.getElementById('minConfidenceValue').textContent = '0%';
            document.getElementById('maxConfidenceValue').textContent = '100%';
            document.getElementById('maxTimeValue').textContent = '10s';
            document.getElementById('filterResultsPreview').textContent = 'Filtres réinitialisés';
            updateActiveFilters();
        }

        // Get filter values
        function getFilterValues() {
            return {
                start_date: document.getElementById('startDate').value,
                end_date: document.getElementById('endDate').value,
                diagnostic_types: Array.from(document.querySelectorAll('.diagnostic-checkbox:checked')).map(cb => cb.value),
                min_confidence: parseInt(document.getElementById('minConfidence').value),
                max_confidence: parseInt(document.getElementById('maxConfidence').value),
                max_processing_time: parseFloat(document.getElementById('maxProcessingTime').value)
            };
        }

        // Apply filters function
        async function applyFiltersFunction() {
            const filters = getFilterValues();

            try {
                const response = await fetch('/api/analytics/filtered', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(filters)
                });

                const data = await response.json();

                if (data.success) {
                    updateFilteredResults(data.data);
                    document.getElementById('filterResultsPreview').textContent =
                        `${data.data.analyses.length} résultats affichés`;
                }
            } catch (error) {
                console.error('Error applying filters:', error);
            }
        }

        function updateFilteredResults(data) {
            const tbody = document.getElementById('filteredAnalysesTable');
            const countSpan = document.getElementById('filteredCount');
            
            countSpan.textContent = `${data.analyses.length} résultats`;
            tbody.innerHTML = '';

            data.analyses.forEach(analysis => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${new Date(analysis.timestamp).toLocaleString('fr-FR')}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${analysis.filename}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getBadgeClass(analysis.predicted_label)}">
                            ${analysis.predicted_label}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div class="flex items-center">
                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: ${analysis.confidence}%"></div>
                            </div>
                            ${analysis.confidence}%
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${analysis.processing_time}s
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                        ${analysis.description}
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function getBadgeClass(label) {
            const classes = {
                'Normal': 'bg-green-100 text-green-800',
                'Gliome': 'bg-red-100 text-red-800',
                'Méningiome': 'bg-yellow-100 text-yellow-800',
                'Tumeur pituitaire': 'bg-purple-100 text-purple-800'
            };
            return classes[label] || 'bg-gray-100 text-gray-800';
        }
    </script>
</body>
</html>
